# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Smart QA is an Obsidian plugin that enables AI-powered question-answering over knowledge bases. The plugin supports multiple AI models (<PERSON>, <PERSON><PERSON>, <PERSON>) through OpenRouter and Google AI APIs, allowing users to have contextual conversations with their entire vault or selected files/folders/tags.

## Development Commands

### Main Plugin Development
```bash
# Install dependencies
npm install

# Development mode with hot reload
npm run dev

# Build for production
npm run build

# Version management
npm run version
```

### Web Preview Development (Fast Debugging)
```bash
# Navigate to web preview
cd web-preview

# Start development server (faster iteration)
npm start
# or
node server.js

# Access at http://localhost:3000
```

## Development Workflows

### Primary: Web Preview for Rapid Development
1. Use `cd web-preview && npm start` for fastest iteration
2. Test all functionality in browser at http://localhost:3000
3. Debug UI, API integration, and feature logic
4. Port working code back to main plugin TypeScript files

### Secondary: Direct Plugin Testing
1. Build the project: `npm run build`
2. Copy the entire plugin folder to your Obsidian vault's `.obsidian/plugins/` directory
3. Enable "Smart QA" in Obsidian settings
4. Use `Cmd/Ctrl + Shift + Q` to open the plugin panel

### Code Porting Between Environments
- **web-preview/assets/js/** contains JavaScript versions of TypeScript modules
- **src/** contains the original TypeScript plugin code
- Key mapping: `ui-controller.js` ↔ `view.ts`, `api-service.js` ↔ `api.ts`, etc.

## Architecture Overview

### Dual Environment Structure

This project maintains both plugin and web preview versions:

**Plugin Environment (TypeScript)**
- **main.ts**: Plugin entry point, handles plugin lifecycle and view registration
- **src/view.ts**: Main UI component (SmartQAView) - sidebar panel with chat interface
- **src/settings.ts**: Configuration management and settings UI with model management
- **src/api.ts**: AI service abstraction layer supporting multiple providers
- **src/file-processor.ts**: Handles file selection, content processing, and token management
- **src/conversation-manager.ts**: Manages conversation history and note generation

**Web Preview Environment (JavaScript)**
- **web-preview/assets/js/main.js**: Application initialization and configuration panel
- **web-preview/assets/js/ui-controller.js**: Browser port of SmartQAView with identical functionality
- **web-preview/assets/js/api-service.js**: Direct port of AI service layer
- **web-preview/assets/js/obsidian-mock.js**: Complete Obsidian API simulation
- **web-preview/assets/js/mock-data.js**: 9 sample markdown files for testing

### Build System

- **esbuild.config.js**: Build configuration using esbuild for fast compilation
- **tsconfig.json**: TypeScript configuration
- **manifest.json**: Obsidian plugin manifest
- **web-preview/server.js**: Local HTTP server with CORS support

### Key Design Patterns

1. **Provider Pattern**: AI service abstraction allows easy addition of new AI providers
2. **Observer Pattern**: Settings changes notify views to update AI service instances
3. **Strategy Pattern**: File selection supports multiple modes (all/folder/tags)
4. **Token Management**: Built-in content truncation to stay within model context limits

### File Processing Flow

1. User selects files via three modes: All Files, Folder, or Tags
2. FileProcessor retrieves matching files and processes content
3. Content is formatted for AI context with source tracking
4. Token estimation and truncation prevents context overflow

### AI Integration

- Supports streaming and non-streaming responses across both environments
- Model validation during configuration
- Provider-specific token estimation
- Automatic fallback mechanisms for failed streams
- OpenRouter supports: Claude 3.5 Sonnet, GPT-4o, Gemini Pro 1.5
- Google AI supports: Gemini Pro, Gemini 1.5 Pro

### State Management

- Conversation history maintained per session
- File selection state persists during conversation
- Model selection integrated with settings
- LocalStorage persistence in web preview environment

## Critical Implementation Details

### Cross-Environment Consistency
- Both environments implement identical UI behavior and API integration
- Obsidian API mocked completely in web preview for seamless testing
- CSS variables ensure theme compatibility across light/dark modes
- Streaming responses work identically in both environments

### Token Management Strategy
- Content truncation at 80% of model context limit
- Smart file prioritization (smaller files first)
- Real-time token estimation for user input
- Sentence-boundary truncation to preserve readability

### Error Handling Patterns
- Graceful fallback from streaming to non-streaming responses
- API key validation with user-friendly error messages
- Model availability checking before requests
- Network failure recovery with retry mechanisms

## API Keys Required

- OpenRouter API Key for accessing multiple models (Claude, GPT, etc.)
- Google AI API Key for Gemini models
- At least one API key must be configured for the plugin to function

## 语言指令

- 请用中文回答我：Claude已记录此指令，将默认使用中文进行交互和回复

## 构建指令

- 每次完成后帮我构建一下：在每次开发迭代或功能完成后，自动执行构建流程，确保项目代码及时编译和更新