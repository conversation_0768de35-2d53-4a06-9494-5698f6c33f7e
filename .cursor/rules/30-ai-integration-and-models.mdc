---
description: AI 集成、模型 ID 规范与流式输出（OpenRouter / Google AI）
---
# AI 集成与模型规范

## 服务与提供方
- 统一入口：`AIService`（[src/api.ts](mdc:src/api.ts)）按 `model` 前缀路由到 Provider：
  - `openrouter/*` → `OpenRouterProvider`
  - `google/*` → `GoogleAIProvider`
- `AIService.updateKeys()` 用于在设置变化后热更新 Provider 实例（见 [main.ts](mdc:main.ts) 通知视图刷新）。

## 模型 ID 规则
- UI 存储“模型记录”使用 `ModelConfig`（[src/settings.ts](mdc:src/settings.ts)）：`provider` + `modelId` + `temperature`
- 调用时拼接为 `fullModelId = ${provider}/${modelId}`，例如：
  - `openrouter/anthropic/claude-3.5-sonnet`
  - `google/gemini-1.5-pro`
- 校验模型使用 `AIService.validateModel(fullModelId, temperature)`；失败需提示错误信息。

## 请求与流式
- 首选流式：`for await (const chunk of aiService.streamChat(messages, fullModelId))`
  - 结束条件：`chunk.done === true`
  - 错误：`chunk.error` 不为空即系统消息提示
- 回退到非流式：`aiService.chat(messages, fullModelId, false)`
- 所有请求需包含系统上下文（知识库格式化结果），系统消息在 `messages[0]`。

## Token 估算
- 调用 `aiService.estimateTokens(text, fullModelId)`；Provider 内部有差异化估算。
- 上下文总 Token 限制约束由设置 `maxContextTokens` 管理，保留 20% 给对话（见 [src/view.ts](mdc:src/view.ts)）。

## 错误与鲁棒性
- OpenRouter/Google 返回非 2xx 时，读取文本并返回结构化错误信息。
- 流式解析使用行缓冲；遇到无法解析的片段应忽略（不终止）。