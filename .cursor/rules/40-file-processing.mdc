---
description: 文件选择与预处理、标签计数、上下文格式化与截断策略
---
# 文件处理与上下文构建

## 选择范围
- `FileProcessor.getFilesBySelection(mode, selection)`（[src/file-processor.ts](mdc:src/file-processor.ts)）
  - `all`：全部 Markdown 文件
  - `folder`：以路径前缀过滤
  - `tags`：使用 `metadataCache` + `getAllTags` 过滤

## 预处理
- `processFile`：读取内容、抽取 `CachedMetadata`、统计标签与词数
- `preprocessContent`：
  - 去除多余空行、YAML front matter
  - 转换内部链接 `[[Link]]` 与 `#tag` → `tag:tag`
  - 清理 HTML、统一换行、收敛标点

## 批处理与摘要
- `processFiles` 限制 `maxFiles`（默认 100），生成 `summary`：
  - `Files | Words | Folders | Tags`

## Token 估算与截断
- 估算：`estimateTokens` 考虑英文/中文/其它字符差异
- 截断：`truncateContent(processed, maxTokens)` 按文件大小升序装填；必要时对单文件 `truncateText`
- 视图侧为对话预留 20% Token（见 [src/view.ts](mdc:src/view.ts)）。

## 上下文格式化
- `formatForAI(processed, includeFileNames)` 生成：
  - 头部 `=== KNOWLEDGE BASE CONTENT (N files) ===`
  - 每个文件的 `File/Path/Tags` + 内容 + `---` 分隔
- 系统提示在 `messages[0]` 注入（见 [src/view.ts](mdc:src/view.ts)）。