---
alwaysApply: true
---
# Smart QA 项目结构与入口

本项目是 Obsidian 插件（TypeScript + esbuild 打包）。主入口为 [main.ts](mdc:main.ts)，构建为 [main.js](mdc:main.js)，清单在 [manifest.json](mdc:manifest.json)。

## 构建与运行
- 开发：`npm run dev` 使用 [esbuild.config.js](mdc:esbuild.config.js) 监听并输出 CJS 到 `main.js`
- 构建：`npm run build`

## 重要模块
- 视图与 UI：[src/view.ts](mdc:src/view.ts)
- 设置与模型管理：[src/settings.ts](mdc:src/settings.ts)
- AI 提供方与服务：[src/api.ts](mdc:src/api.ts)
- 文件处理与上下文收集：[src/file-processor.ts](mdc:src/file-processor.ts)
- 对话管理与保存：[src/conversation-manager.ts](mdc:src/conversation-manager.ts)

## 运行时关键点
- 插件类：`SmartQAPlugin` 定义于 [main.ts](mdc:main.ts)，注册视图 `VIEW_TYPE_SMART_QA`
- 侧边栏视图：`SmartQAView` 定义于 [src/view.ts](mdc:src/view.ts)
- 设置页：`SmartQASettingTab` 管理 API Key、模型增删改查与默认模型
- 构建外部依赖：`obsidian` 与 Node 内建模块通过 esbuild external 处理