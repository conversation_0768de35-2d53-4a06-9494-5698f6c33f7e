---
globs: *.ts
---
# TypeScript 编码与风格规范（Smart QA）

- 严格类型：导出 API 与类公开方法必须显式类型标注。
- 命名：函数为动词/短语，变量为名词短语，避免缩写（见 [src/view.ts](mdc:src/view.ts), [src/api.ts](mdc:src/api.ts)）。
- 控制流：优先早返回；错误优先处理；避免 3 层以上嵌套。
- 错误处理：禁止无意义 try/catch；捕获后返回结构化错误（参考 `AIResponse.error` in [src/api.ts](mdc:src/api.ts)）。
- 注释：解释“为何”，避免赘述“如何”；不写行尾注释；必要时函数上方简短说明。
- 格式：保持现有代码风格；多行优于长行内联；不要重排无关代码。
- Token/字符串处理：使用工具方法集中处理（见 `estimateTokens` in [src/file-processor.ts](mdc:src/file-processor.ts)）。
- 公共常量/枚举：优先集中声明；ID 前缀如 `VIEW_TYPE_SMART_QA` 放在模块顶层（见 [src/view.ts](mdc:src/view.ts)）。
- Obsidian API：类型来自 `obsidian`，避免 `any` 与非必要断言；DOM 操作用 Obsidian 提供的 `createEl` 与类名样式。