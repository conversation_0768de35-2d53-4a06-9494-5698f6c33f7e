---
description: Obsidian 插件生命周期、视图注册、设置页与样式注入约定
---
# Obsidian 插件约定（Smart QA）

## 生命周期
- 插件主类位于 [main.ts](mdc:main.ts) 的 `SmartQAPlugin`
- `onload()`：
  - `loadSettings()` 合并 `DEFAULT_SETTINGS`（[src/settings.ts](mdc:src/settings.ts)）
  - `registerView(VIEW_TYPE_SMART_QA, ...)` 注册视图（[src/view.ts](mdc:src/view.ts)）
  - `addRibbonIcon`、`addCommand`、`addSettingTab`
  - `workspace.onLayoutReady(() => activateView())` 首次激活
- `onunload()`：`detachLeavesOfType(VIEW_TYPE_SMART_QA)`

## 视图
- 视图类型常量：`VIEW_TYPE_SMART_QA`（[src/view.ts](mdc:src/view.ts)）
- 视图类：`SmartQAView extends ItemView`
  - 在 `onOpen()` 中构建 UI：Header、FileSelection、Messages、Status、Input
  - UI 使用 `createEl` 与类名，样式通过注入 `<style id="smart-qa-styles">`

## 设置页
- `SmartQASettingTab`（[main.ts](mdc:main.ts)）
  - 管理 OpenRouter/Google API Key
  - 模型列表渲染、编辑、测试、删除，新增模型表单
  - 默认模型下拉与“其他设置”（Hybrid 模式、Max Context Tokens、Notes Folder）
  - 样式通过 `<style id="smart-qa-settings-styles">` 注入，确保只注入一次

## 规则
- 样式注入需使用唯一 `id` 并检查是否已存在。
- 与设置变更相关逻辑调用 `plugin.saveSettings()` 后，通知视图 `updateAIService()`（[main.ts](mdc:main.ts)）。
- 激活视图逻辑优先复用既有 leaf：`getLeavesOfType` > `getRightLeaf(false)` > `revealLeaf`。