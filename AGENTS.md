# Repository Guidelines

## 项目结构与模块组织
主逻辑存放于 `main.ts` 和 `src/`，其中 `src/view.ts` 负责 Smart QA 视图、样式注入与事件绑定，`src/api.ts`、`conversation-manager.ts` 等文件拆分服务层逻辑。`web-preview/` 提供浏览器预览的纯 web 版本（`assets/js/ui-controller.js` 与 `assets/css/smart-qa.css`），便于无 Obsidian 环境调试。`tests/` 目录保存 Playwright 端到端用例，`preview/obsidian-mock.ts` 负责在 `npm run dev` 时模拟 Obsidian API。静态资源与打包产物位于 `web-preview/assets/` 与根目录下的 `main.js`。

## 构建、测试与开发命令
- `npm install`：安装依赖，首次克隆后必跑。
- `npm run dev`：调用 `esbuild.config.js` 进入 watch 模式，实时刷新 `main.js` 与 `web-preview` 预览文件。
- `npm run build`：执行一次性生产构建，生成最新版 `main.js` 供插件分发。
- `npx playwright test`：运行 `tests/*.spec.js` 中的 UI 检查，如需 headed 调试可追加 `--headed`。

## 编码风格与命名
TypeScript/JavaScript 统一使用 Tab 缩进与尾随分号（延续现有文件），类名与组件使用 PascalCase，函数与变量采用 camelCase，CSS 类保持 `smart-qa-*` 前缀以避免与主题冲突。保持模块内职责单一：视图仅处理 DOM，数据与会话逻辑放在对应 service。提交前请确保 `npm run build` 无错误以捕捉类型问题。

## 测试指引
端到端测试基于 Playwright，文件命名使用 `*.spec.js` 与语义化用例描述（如 `should toggle tag filter`）。新增功能需添加或更新对应测试并在 PR 中贴上命令输出。若引入快照或截图，请将基线放入 `tests/` 或 `playwright-report/` 并在文档中说明移除策略。

## 提交与合并准则
仓库当前无公开提交记录，推荐遵循 Conventional Commits（如 `feat: support font scaling`、`fix: clean modal close button`）。PR 描述应概述问题、解决方案与测试方式，涉及 UI 的改动附上预览截图或录屏。确保将敏感凭据放入用户设置而非仓库文件，并在 PR 中注明是否需要更新 `manifest.json` 的版本号。

## 配置与安全提示
API Key 仅通过插件设置面板注入，不应写入代码库；若需本地调试，可使用 `.env.local`（自建且加入 `.gitignore`）。构建生成的 `main.js` 与 `web-preview` 产物会被提交，发布前请确认版本号与 `versions.json` 一致，避免 Obsidian 在加载时出现缓存错配。
