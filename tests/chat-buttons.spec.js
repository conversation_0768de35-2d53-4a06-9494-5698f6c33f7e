// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('聊天气泡按钮位置', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到本地应用
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('复制和转为笔记按钮应该位于气泡左下角', async ({ page }) => {
    // 发送一个测试消息以获得AI回复
    const inputArea = page.locator('.smart-qa-input');
    await inputArea.fill('你好，请简单回复一下');
    
    const sendButton = page.locator('.smart-qa-send-btn');
    await sendButton.click();
    
    // 等待AI回复消息出现
    await page.waitForSelector('.smart-qa-message-assistant', { timeout: 30000 });
    
    // 找到助手的回复气泡
    const assistantMessage = page.locator('.smart-qa-message-assistant').first();
    const messageContent = assistantMessage.locator('.smart-qa-message-content');
    const messageActions = assistantMessage.locator('.smart-qa-message-actions');
    
    // 验证消息内容存在
    await expect(messageContent).toBeVisible();
    
    // 验证按钮组存在
    await expect(messageActions).toBeVisible();
    
    // 获取消息气泡和按钮组的边界框
    const contentBox = await messageContent.boundingBox();
    const actionsBox = await messageActions.boundingBox();
    
    // 验证按钮组位于气泡左下角
    expect(actionsBox).toBeTruthy();
    expect(contentBox).toBeTruthy();
    
    // 按钮组应该在气泡内部的左下角位置
    expect(actionsBox.x).toBeGreaterThanOrEqual(contentBox.x);
    expect(actionsBox.x).toBeLessThan(contentBox.x + contentBox.width / 2); // 在左半部分
    expect(actionsBox.y).toBeGreaterThan(contentBox.y + contentBox.height - 50); // 接近底部
    expect(actionsBox.y + actionsBox.height).toBeLessThanOrEqual(contentBox.y + contentBox.height);
  });

  test('复制按钮功能正常', async ({ page }) => {
    // 发送测试消息
    const inputArea = page.locator('.smart-qa-input');
    await inputArea.fill('请回复"测试复制功能"');
    
    const sendButton = page.locator('.smart-qa-send-btn');
    await sendButton.click();
    
    // 等待AI回复
    await page.waitForSelector('.smart-qa-message-assistant', { timeout: 30000 });
    
    // 点击复制按钮
    const copyButton = page.locator('.smart-qa-message-assistant .smart-qa-btn-icon-action').first();
    await copyButton.click();
    
    // 验证复制成功（可能需要检查系统剪贴板或查看通知）
    await expect(copyButton).toBeVisible();
  });

  test('转为笔记按钮功能正常', async ({ page }) => {
    // 发送测试消息
    const inputArea = page.locator('.smart-qa-input');
    await inputArea.fill('这是一个测试消息');
    
    const sendButton = page.locator('.smart-qa-send-btn');
    await sendButton.click();
    
    // 等待AI回复
    await page.waitForSelector('.smart-qa-message-assistant', { timeout: 30000 });
    
    // 点击转为笔记按钮（第二个按钮）
    const noteButton = page.locator('.smart-qa-message-assistant .smart-qa-btn-icon-action').nth(1);
    await noteButton.click();
    
    // 验证按钮可点击
    await expect(noteButton).toBeVisible();
  });

  test('按钮在不同主题下显示正常', async ({ page }) => {
    // 测试浅色主题
    await page.emulateMedia({ colorScheme: 'light' });
    
    const inputArea = page.locator('.smart-qa-input');
    await inputArea.fill('主题测试');
    
    const sendButton = page.locator('.smart-qa-send-btn');
    await sendButton.click();
    
    await page.waitForSelector('.smart-qa-message-assistant', { timeout: 30000 });
    
    const messageActions = page.locator('.smart-qa-message-assistant .smart-qa-message-actions').first();
    await expect(messageActions).toBeVisible();
    
    // 测试深色主题
    await page.emulateMedia({ colorScheme: 'dark' });
    await expect(messageActions).toBeVisible();
  });
});