import { access, copyFile } from 'fs/promises';
import { constants } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const targetDir = process.env.SMART_QA_PLUGIN_DIR
  || '/Users/<USER>/Documents/Obsidian+AI/Obsidian+AI/.obsidian/plugins/smart';

const sourceFile = fileURLToPath(new URL('../main.js', import.meta.url));
const targetFile = path.join(targetDir, 'main.js');

async function ensureTargetDirectory(dir) {
  try {
    await access(dir, constants.R_OK | constants.W_OK);
  } catch (error) {
    throw new Error(`目标目录不存在或不可写: ${dir}`);
  }
}

async function copyBundle() {
  if (process.env.SKIP_SMART_QA_COPY === '1') {
    console.info('[postbuild] 检测到 SKIP_SMART_QA_COPY=1，跳过 main.js 拷贝');
    return;
  }

  await ensureTargetDirectory(targetDir);
  await copyFile(sourceFile, targetFile);
  console.info(`[postbuild] 已复制 ${path.basename(sourceFile)} 到 ${targetFile}`);
}

copyBundle().catch((error) => {
  console.error('[postbuild] main.js 拷贝失败:', error.message);
  process.exitCode = 1;
});
