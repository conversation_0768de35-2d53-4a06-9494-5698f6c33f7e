import { App, Plugin, PluginSettingTab, Setting, WorkspaceLeaf, Notice } from 'obsidian';
import { SmartQAView, VIEW_TYPE_SMART_QA } from './src/view';
import { SmartQASettings, DEFAULT_SETTINGS, ModelConfig, addModel, removeModel, updateModel, generateModelId, getModelsByProvider } from './src/settings';
import { AIService } from './src/api';

export default class SmartQAPlugin extends Plugin {
	settings: SmartQASettings;

	async onload() {
		// 加载设置
		await this.loadSettings();

		// 注册视图类型
		this.registerView(
			VIEW_TYPE_SMART_QA,
			(leaf) => new SmartQAView(leaf, this)
		);

		// 添加侧边栏面板
		this.addRibbonIcon('message-circle', 'Smart QA', (evt: MouseEvent) => {
			this.activateView();
		});

		// 添加快捷键
		this.addCommand({
			id: 'open-smart-qa',
			name: 'Open Smart QA Panel',
			callback: () => {
				this.activateView();
			},
			hotkeys: [
				{
					modifiers: ['Mod', 'Shift'],
					key: 'q'
				}
			]
		});

		// 添加设置页面
		this.addSettingTab(new SmartQASettingTab(this.app, this));

		// 初始化时激活视图
		this.app.workspace.onLayoutReady(() => {
			this.activateView();
		});
	}

	onunload() {
		this.app.workspace.detachLeavesOfType(VIEW_TYPE_SMART_QA);
	}

	async activateView() {
		const { workspace } = this.app;
		
		let leaf: WorkspaceLeaf | null = null;
		const leaves = workspace.getLeavesOfType(VIEW_TYPE_SMART_QA);

		if (leaves.length > 0) {
			// 如果已存在，激活现有的
			leaf = leaves[0];
		} else {
			// 创建新的侧边栏面板
			leaf = workspace.getRightLeaf(false);
			await leaf?.setViewState({ type: VIEW_TYPE_SMART_QA, active: true });
		}

		// 激活面板
		if (leaf) {
			workspace.revealLeaf(leaf);
		}
	}

	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);
		this.notifyViewsOfSettingsChange();
	}

	private notifyViewsOfSettingsChange() {
		const leaves = this.app.workspace.getLeavesOfType(VIEW_TYPE_SMART_QA);
		leaves.forEach(leaf => {
			const view = leaf.view as SmartQAView;
			if (view && typeof view.updateAIService === 'function') {
				view.updateAIService();
			}
		});
	}
}

class SmartQASettingTab extends PluginSettingTab {
	plugin: SmartQAPlugin;
	private aiService: AIService;

	constructor(app: App, plugin: SmartQAPlugin) {
		super(app, plugin);
		this.plugin = plugin;
		this.aiService = new AIService(
			plugin.settings.openRouterApiKey,
			plugin.settings.googleAiApiKey
		);
	}

	display(): void {
		const { containerEl } = this;
		containerEl.empty();

		containerEl.createEl('h2', { text: 'Smart QA Settings' });

		// OpenRouter 模型管理
		this.createProviderSection(containerEl, 'openrouter');

		// Google AI 模型管理
		this.createProviderSection(containerEl, 'google');

		// 默认模型设置
		this.createDefaultModelSetting(containerEl);

		// 其他设置
		this.createOtherSettings(containerEl);
	}

	private createProviderSection(containerEl: HTMLElement, provider: 'openrouter' | 'google') {
		const providerName = provider === 'openrouter' ? 'OpenRouter' : 'Google AI';
		const apiKeyField = provider === 'openrouter' ? 'openRouterApiKey' : 'googleAiApiKey';
		const placeholder = provider === 'openrouter' ? 'sk-or-...' : 'AIza...';

		// 创建分组容器
		const sectionEl = containerEl.createEl('div', { cls: 'model-section' });
		sectionEl.createEl('h3', { text: `${providerName} Models` });

		// API Key 设置
		new Setting(sectionEl)
			.setName(`${providerName} API Key`)
			.setDesc(`Enter your ${providerName} API key`)
			.addText(text => text
				.setPlaceholder(placeholder)
				.setValue(this.plugin.settings[apiKeyField])
				.onChange(async (value) => {
					this.plugin.settings[apiKeyField] = value;
					await this.plugin.saveSettings();
					this.aiService.updateKeys(
						this.plugin.settings.openRouterApiKey,
						this.plugin.settings.googleAiApiKey
					);
				}));

		// 已添加的模型列表
		const modelsContainer = sectionEl.createEl('div', { cls: 'models-container' });
		const modelsList = modelsContainer.createEl('div', { cls: 'models-list' });
		
		this.renderModelsList(modelsList, provider);

		// 添加新模型表单
		this.createAddModelForm(modelsContainer, provider);
	}

	private renderModelsList(container: HTMLElement, provider: 'openrouter' | 'google') {
		container.empty();

		const models = getModelsByProvider(this.plugin.settings, provider);
		
		if (models.length === 0) {
			container.createEl('div', { 
				text: 'No models configured',
				cls: 'no-models-message'
			});
			return;
		}

		models.forEach(model => {
			const modelItem = container.createEl('div', { cls: 'model-item' });
			
			const modelInfo = modelItem.createEl('div', { cls: 'model-info' });
			modelInfo.createEl('div', { text: model.name, cls: 'model-name' });
			modelInfo.createEl('div', { text: `ID: ${model.modelId} | Temp: ${model.temperature}`, cls: 'model-details' });
			
			const modelActions = modelItem.createEl('div', { cls: 'model-actions' });
			
			// 编辑按钮
			const editBtn = modelActions.createEl('button', { text: 'Edit', cls: 'model-btn model-btn-secondary' });
			editBtn.addEventListener('click', () => this.editModel(model));
			
			// 测试按钮
			const testBtn = modelActions.createEl('button', { text: 'Test', cls: 'model-btn model-btn-secondary' });
			testBtn.addEventListener('click', () => this.testModel(model));
			
			// 删除按钮
			const deleteBtn = modelActions.createEl('button', { text: 'Delete', cls: 'model-btn model-btn-danger' });
			deleteBtn.addEventListener('click', () => this.deleteModel(model, provider));
		});
	}

	private createAddModelForm(container: HTMLElement, provider: 'openrouter' | 'google') {
		const formContainer = container.createEl('details', { cls: 'add-model-form' });
		formContainer.createEl('summary', { text: 'Add New Model' });
		
		const form = formContainer.createEl('div', { cls: 'form-content' });
		
		// 模型ID输入
		const modelIdInput = form.createEl('input', { 
			type: 'text',
			placeholder: provider === 'openrouter' ? 'anthropic/claude-3.5-sonnet' : 'gemini-pro',
			cls: 'model-input'
		});
		form.insertBefore(form.createEl('label', { text: 'Model ID:', cls: 'model-label' }), modelIdInput);
		
		// 自定义名称输入
		const nameInput = form.createEl('input', { 
			type: 'text',
			placeholder: 'My Custom Model',
			cls: 'model-input'
		});
		form.insertBefore(form.createEl('label', { text: 'Display Name:', cls: 'model-label' }), nameInput);
		
		// 温度滑块
		const tempContainer = form.createEl('div', { cls: 'temp-container' });
		tempContainer.createEl('label', { text: 'Temperature:', cls: 'model-label' });
		const tempSlider = tempContainer.createEl('input', { 
			type: 'range',
			cls: 'temp-slider'
		}) as HTMLInputElement;
		tempSlider.min = '0';
		tempSlider.max = '2';
		tempSlider.step = '0.1';
		tempSlider.value = '0.7';
		const tempValue = tempContainer.createEl('span', { text: '0.7', cls: 'temp-value' });
		
		tempSlider.addEventListener('input', () => {
			tempValue.textContent = tempSlider.value;
		});
		
		// 按钮组
		const buttonGroup = form.createEl('div', { cls: 'button-group' });
		
		// 测试按钮
		const testBtn = buttonGroup.createEl('button', { text: 'Test', cls: 'model-btn model-btn-secondary' });
		testBtn.addEventListener('click', async () => {
			const modelId = modelIdInput.value.trim();
			const temperature = parseFloat(tempSlider.value);
			
			if (!modelId) {
				new Notice('Please enter a model ID');
				return;
			}
			
			await this.validateModelId(provider, modelId, temperature);
		});
		
		// 添加按钮
		const addBtn = buttonGroup.createEl('button', { text: 'Add', cls: 'model-btn model-btn-primary' });
		addBtn.addEventListener('click', async () => {
			const modelId = modelIdInput.value.trim();
			const name = nameInput.value.trim();
			const temperature = parseFloat(tempSlider.value);
			
			if (!modelId || !name) {
				new Notice('Please fill in all required fields');
				return;
			}
			
			// 先验证模型
			const validation = await this.validateModelId(provider, modelId, temperature);
			if (!validation) return;
			
			// 添加模型
			const newModel: ModelConfig = {
				id: generateModelId(),
				name,
				provider,
				modelId,
				temperature
			};
			
			addModel(this.plugin.settings, newModel);
			await this.plugin.saveSettings();
			
			// 清空表单
			modelIdInput.value = '';
			nameInput.value = '';
			tempSlider.value = '0.7';
			tempValue.textContent = '0.7';
			
			// 重新渲染模型列表
			const modelsList = container.querySelector('.models-list') as HTMLElement;
			if (modelsList) {
				this.renderModelsList(modelsList, provider);
			}
			
			// 更新默认模型下拉框
			this.updateDefaultModelDropdown();
			
			new Notice(`Model "${name}" added successfully!`);
		});
	}

	private async validateModelId(provider: 'openrouter' | 'google', modelId: string, temperature: number): Promise<boolean> {
		const fullModelId = `${provider}/${modelId}`;
		
		try {
			new Notice('Testing model...', 2000);
			const result = await this.aiService.validateModel(fullModelId, temperature);
			
			if (result.valid) {
				new Notice('✅ Model validation successful!');
				return true;
			} else {
				new Notice(`❌ Model validation failed: ${result.error}`);
				return false;
			}
		} catch (error) {
			new Notice(`❌ Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
			return false;
		}
	}

	private editModel(model: ModelConfig) {
		// 创建编辑对话框的简单实现
		const newName = prompt('Enter new display name:', model.name);
		if (newName && newName !== model.name) {
			updateModel(this.plugin.settings, model.id, { name: newName });
			this.plugin.saveSettings();
			this.display(); // 重新渲染
			new Notice('Model updated!');
		}
	}

	private async testModel(model: ModelConfig) {
		const fullModelId = `${model.provider}/${model.modelId}`;
		await this.validateModelId(model.provider, model.modelId, model.temperature);
	}

	private deleteModel(model: ModelConfig, provider: 'openrouter' | 'google') {
		if (confirm(`Are you sure you want to delete "${model.name}"?`)) {
			removeModel(this.plugin.settings, model.id);
			this.plugin.saveSettings();
			
			// 重新渲染模型列表
			const modelsList = this.containerEl.querySelector(`#${provider}-models-list`) as HTMLElement;
			if (modelsList) {
				this.renderModelsList(modelsList, provider);
			}
			
			// 更新默认模型下拉框
			this.updateDefaultModelDropdown();
			
			new Notice(`Model "${model.name}" deleted!`);
			this.display(); // 重新渲染整个页面
		}
	}

	private createDefaultModelSetting(containerEl: HTMLElement) {
		const setting = new Setting(containerEl)
			.setName('Default Model')
			.setDesc('Select the default AI model to use in conversations');
		
		this.defaultModelDropdown = setting.addDropdown(dropdown => {
			this.populateDefaultModelOptions(dropdown);
			dropdown.setValue(this.plugin.settings.defaultModel)
				.onChange(async (value) => {
					this.plugin.settings.defaultModel = value;
					await this.plugin.saveSettings();
				});
			return dropdown;
		});
	}

	private defaultModelDropdown: any;

	private populateDefaultModelOptions(dropdown: any) {
		dropdown.selectEl.empty();
		
		if (this.plugin.settings.customModels.length === 0) {
			dropdown.addOption('', 'No models configured');
			return;
		}
		
		this.plugin.settings.customModels.forEach(model => {
			dropdown.addOption(model.id, model.name);
		});
	}

	private updateDefaultModelDropdown() {
		if (this.defaultModelDropdown) {
			this.populateDefaultModelOptions(this.defaultModelDropdown);
		}
	}

	private createOtherSettings(containerEl: HTMLElement) {
		containerEl.createEl('h3', { text: 'Other Settings' });

		new Setting(containerEl)
			.setName('Enable Hybrid Mode')
			.setDesc('Use keyword pre-filtering to reduce token usage')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.enableHybridMode)
				.onChange(async (value) => {
					this.plugin.settings.enableHybridMode = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('Max Context Tokens')
			.setDesc('Maximum tokens to send in context (affects cost)')
			.addText(text => text
				.setPlaceholder('100000')
				.setValue(this.plugin.settings.maxContextTokens.toString())
				.onChange(async (value) => {
					const num = parseInt(value);
					if (!isNaN(num) && num > 0) {
						this.plugin.settings.maxContextTokens = num;
						await this.plugin.saveSettings();
					}
				}));

		new Setting(containerEl)
			.setName('UI Font Size')
			.setDesc('Adjust Smart QA panel typography (12–20 px)')
			.addSlider(slider => {
				slider
					.setLimits(12, 20, 1)
					.setValue(this.plugin.settings.uiFontSize ?? 14)
					.setDynamicTooltip();

				const sliderContainer = slider.sliderEl.parentElement as HTMLElement | null;
				let valueLabel: HTMLElement | null = null;
				if (sliderContainer) {
					sliderContainer.classList.add('smart-qa-slider-control');
					valueLabel = sliderContainer.createEl('span', {
						cls: 'smart-qa-slider-value'
					});
				} else {
					console.warn('[SmartQA] Slider container not found, value label will not be rendered.');
				}

				const clampAndRound = (value: number) => Math.min(20, Math.max(12, Math.round(value)));

				const updateLabel = (value: number) => {
					if (valueLabel) valueLabel.textContent = `${value}px`;
				};

				const persistValue = async (value: number) => {
					const normalized = clampAndRound(value);
					const currentSliderValue = slider.getValue();
					if (currentSliderValue !== normalized) {
						slider.setValue(normalized);
					}
					updateLabel(normalized);
					if (this.plugin.settings.uiFontSize !== normalized) {
						this.plugin.settings.uiFontSize = normalized;
						try {
							await this.plugin.saveSettings();
							console.debug('[SmartQA] UI font size saved:', normalized);
						} catch (error) {
							console.error('[SmartQA] Failed to save font size:', error);
						}
					}
				};

				void persistValue(slider.getValue());

				slider.onChange((value) => {
					void persistValue(value);
				});

				return slider;
			});

		new Setting(containerEl)
			.setName('Notes Folder')
			.setDesc('Folder where conversation notes will be saved')
			.addText(text => text
				.setPlaceholder('Smart QA Conversations')
				.setValue(this.plugin.settings.notesFolder)
				.onChange(async (value) => {
					this.plugin.settings.notesFolder = value;
					await this.plugin.saveSettings();
				}));

		// 添加样式
		if (!document.querySelector('#smart-qa-settings-styles')) {
			const style = document.createElement('style');
			style.id = 'smart-qa-settings-styles';
			style.textContent = this.getSettingsCSS();
			document.head.appendChild(style);
		}
	}

	private getSettingsCSS(): string {
		return `
		.model-section {
			margin: 20px 0;
			padding: 16px;
			border: 1px solid var(--background-modifier-border);
			border-radius: 8px;
		}

		.model-section h3 {
			margin: 0 0 16px 0;
			color: var(--text-normal);
			font-size: 16px;
			font-weight: 600;
		}

		.models-container {
			margin-top: 16px;
		}

		.models-list {
			display: flex;
			flex-direction: column;
			gap: 8px;
			margin-bottom: 16px;
		}

		.model-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12px;
			border: 1px solid var(--background-modifier-border);
			border-radius: 6px;
			background: var(--background-secondary);
		}

		.model-info {
			flex: 1;
		}

		.model-name {
			font-weight: 500;
			margin-bottom: 4px;
		}

		.model-details {
			font-size: 12px;
			color: var(--text-muted);
		}

		.model-actions {
			display: flex;
			gap: 6px;
		}

		.model-btn {
			padding: 4px 8px;
			border-radius: 4px;
			border: 1px solid var(--background-modifier-border);
			background: var(--background-primary);
			color: var(--text-normal);
			cursor: pointer;
			font-size: 12px;
		}

		.smart-qa-slider-control {
			display: flex;
			align-items: center;
			gap: 12px;
		}

		.smart-qa-slider-value {
			margin-left: 12px;
			min-width: 36px;
			text-align: right;
			color: var(--text-muted);
			font-size: 12px;
		}

		.model-btn:hover {
			background: var(--background-modifier-hover);
		}

		.model-btn-primary {
			background: var(--interactive-accent);
			color: var(--text-on-accent);
			border-color: var(--interactive-accent);
		}

		.model-btn-danger {
			background: #dc3545;
			color: white;
			border-color: #dc3545;
		}

		.no-models-message {
			padding: 16px;
			text-align: center;
			color: var(--text-muted);
			font-style: italic;
		}

		.add-model-form {
			border: 1px solid var(--background-modifier-border);
			border-radius: 6px;
			padding: 0;
			margin-top: 8px;
		}

		.add-model-form summary {
			padding: 12px;
			cursor: pointer;
			font-weight: 500;
			background: var(--background-modifier-hover);
		}

		.form-content {
			padding: 16px;
			display: flex;
			flex-direction: column;
			gap: 12px;
		}

		.model-label {
			font-weight: 500;
			margin-bottom: 4px;
			display: block;
		}

		.model-input {
			width: 100%;
			padding: 8px;
			border: 1px solid var(--background-modifier-border);
			border-radius: 4px;
			background: var(--background-primary);
			color: var(--text-normal);
		}

		.temp-container {
			display: flex;
			flex-direction: column;
			gap: 8px;
		}

		.temp-slider {
			width: 100%;
		}

		.temp-value {
			align-self: flex-end;
			font-weight: 500;
			color: var(--interactive-accent);
		}

		.button-group {
			display: flex;
			gap: 8px;
			justify-content: flex-end;
		}
		`;
	}
}
