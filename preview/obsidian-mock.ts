// Minimal Obsidian API mocks for browser preview

export interface CachedMetadata {
    tags?: string[];
}

export class TFile {
    path: string;
    name: string;
    parent?: { path: string } | null;
    constructor(path: string) {
        this.path = path;
        const parts = path.split('/');
        this.name = parts[parts.length - 1];
        const parentPath = parts.slice(0, -1).join('/') || '/';
        this.parent = { path: parentPath };
    }
}

function extractTagsFromContent(content: string): string[] {
    const tagSet = new Set<string>();
    const tagRegex = /(^|\s)#([\w-]+)/g;
    let match: RegExpExecArray | null;
    while ((match = tagRegex.exec(content))) {
        tagSet.add(`#${match[2]}`);
    }
    return Array.from(tagSet);
}

export function getAllTags(metadata: CachedMetadata | null | undefined): string[] {
    return metadata?.tags ?? [];
}

class Vault {
    private pathToContent: Map<string, string> = new Map();

    setFiles(files: { path: string; content: string }[]) {
        this.pathToContent.clear();
        for (const f of files) {
            this.pathToContent.set(f.path, f.content);
        }
    }

    getMarkdownFiles(): TFile[] {
        return Array.from(this.pathToContent.keys())
            .filter((p) => p.toLowerCase().endsWith('.md'))
            .map((p) => new TFile(p));
    }

    async read(file: TFile): Promise<string> {
        return this.pathToContent.get(file.path) ?? '';
    }

    async create(path: string, content: string): Promise<TFile> {
        this.pathToContent.set(path, content);
        return new TFile(path);
    }

    async createFolder(_folderPath: string): Promise<void> {
        // No-op for preview
    }

    getAbstractFileByPath(path: string): TFile | null {
        return this.pathToContent.has(path) ? new TFile(path) : null;
    }
}

class MetadataCache {
    private vault: Vault;
    constructor(vault: Vault) {
        this.vault = vault;
    }
    getFileCache(file: TFile): CachedMetadata | null {
        // Derive tags from file content on-demand
        const content = (this.vault as any).pathToContent?.get(file.path) as string | undefined;
        if (!content) return { tags: [] };
        return { tags: extractTagsFromContent(content) };
    }
}

class Workspace {
    getLeaf(): { openFile: (file: TFile) => void } {
        return {
            openFile: (file: TFile) => {
                const url = `data:text/markdown;charset=utf-8,` + encodeURIComponent(`Preview open file:\n${file.path}`);
                window.open(url, '_blank');
            },
        };
    }
    onLayoutReady(cb: () => void) {
        setTimeout(cb, 0);
    }
}

class SettingAPI {
    open() {
        // eslint-disable-next-line no-console
        console.log('[Mock] Open settings panel');
    }
    openTabById(id: string) {
        // eslint-disable-next-line no-console
        console.log(`[Mock] Open settings tab: ${id}`);
    }
}

export class App {
    vault: Vault;
    metadataCache: MetadataCache;
    workspace: Workspace;
    setting: SettingAPI;
    constructor() {
        this.vault = new Vault();
        this.metadataCache = new MetadataCache(this.vault);
        this.workspace = new Workspace();
        this.setting = new SettingAPI();
    }
}

export class WorkspaceLeaf {
    private root: HTMLElement;
    constructor(root: HTMLElement) {
        this.root = root;
    }
    attach(el: HTMLElement) {
        this.root.innerHTML = '';
        this.root.appendChild(el);
    }
}

export class ItemView {
    app: App;
    containerEl: HTMLElement;
    leaf: WorkspaceLeaf;
    constructor(leaf: WorkspaceLeaf) {
        // @ts-ignore
        this.app = (globalThis as any).__obsidianMockApp as App;
        this.leaf = leaf;
        this.containerEl = document.createElement('div');
        const header = document.createElement('div');
        const content = document.createElement('div');
        this.containerEl.appendChild(header);
        this.containerEl.appendChild(content);
        (this.leaf as any).attach?.(this.containerEl);
    }
}

export class Notice {
    private static ensureContainer(): HTMLElement {
        let el = document.getElementById('mock-notice-container');
        if (!el) {
            el = document.createElement('div');
            el.id = 'mock-notice-container';
            el.style.position = 'fixed';
            el.style.right = '16px';
            el.style.bottom = '16px';
            el.style.display = 'flex';
            el.style.flexDirection = 'column-reverse';
            el.style.gap = '8px';
            document.body.appendChild(el);
        }
        return el;
    }
    constructor(message: string, timeout = 2500) {
        const container = Notice.ensureContainer();
        const item = document.createElement('div');
        item.textContent = message;
        item.style.background = 'rgba(0,0,0,0.8)';
        item.style.color = 'white';
        item.style.padding = '8px 12px';
        item.style.borderRadius = '6px';
        item.style.fontSize = '12px';
        item.style.maxWidth = '320px';
        item.style.boxShadow = '0 2px 8px rgba(0,0,0,0.3)';
        container.appendChild(item);
        setTimeout(() => item.remove(), timeout);
    }
}

// Optional classes for compatibility (not used directly in preview but exported to match API)
export class Plugin {
    app: App;
    id = 'smart-qa';
    constructor(app?: App) {
        // @ts-ignore
        this.app = app || (globalThis as any).__obsidianMockApp;
    }
    async loadData(): Promise<any> {
        const raw = localStorage.getItem('smart-qa-data');
        return raw ? JSON.parse(raw) : null;
    }
    async saveData(data: any): Promise<void> {
        localStorage.setItem('smart-qa-data', JSON.stringify(data));
    }
}

export class PluginSettingTab {}
export class Setting {}

// DOM helper polyfills matching Obsidian helpers
declare global {
    interface HTMLElement {
        createEl?: (tag: string, options?: { cls?: string; text?: string; attr?: Record<string, string>; type?: string }) => HTMLElement;
        createSpan?: (options?: { cls?: string; text?: string }) => HTMLSpanElement;
        addClass?: (cls: string) => void;
        removeClass?: (cls: string) => void;
        empty?: () => void;
    }
}

function installDomHelpers() {
    const proto = HTMLElement.prototype as any;
    if (!proto.createEl) {
        proto.createEl = function (tag: string, options?: { cls?: string; text?: string; attr?: Record<string, string>; type?: string }) {
            const el = document.createElement(tag);
            if (options?.cls) el.className = options.cls;
            if (options?.text) el.textContent = options.text;
            if (options?.type) (el as HTMLInputElement).type = options.type;
            if (options?.attr) {
                for (const [k, v] of Object.entries(options.attr)) el.setAttribute(k, String(v));
            }
            this.appendChild(el);
            return el;
        };
    }
    if (!proto.createSpan) {
        proto.createSpan = function (options?: { cls?: string; text?: string }) {
            return this.createEl('span', options);
        };
    }
    if (!proto.addClass) {
        proto.addClass = function (cls: string) {
            (cls || '').split(/\s+/).filter(Boolean).forEach((c: string) => this.classList.add(c));
        };
    }
    if (!proto.removeClass) {
        proto.removeClass = function (cls: string) {
            (cls || '').split(/\s+/).filter(Boolean).forEach((c: string) => this.classList.remove(c));
        };
    }
    if (!proto.empty) {
        proto.empty = function () {
            while (this.firstChild) this.removeChild(this.firstChild);
        };
    }
}

installDomHelpers();



