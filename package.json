{"name": "smart-qa", "version": "1.0.0", "description": "Intelligent knowledge base Q&A plugin for Obsidian powered by AI", "main": "main.js", "type": "module", "scripts": {"dev": "node esbuild.config.js --watch", "build": "node esbuild.config.js", "postbuild": "node scripts/postbuild-copy.mjs", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": ["obsidian", "plugin", "ai", "qa", "knowledge-base", "gpt", "claude", "gemini"], "author": "Smart QA Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.55.0", "@types/node": "^16.11.6", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "^0.25.9", "obsidian": "latest", "tslib": "2.4.0", "typescript": "4.7.4"}, "dependencies": {"tiktoken": "^1.0.10"}}