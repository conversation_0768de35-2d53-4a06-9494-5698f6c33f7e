export interface AIMessage {
	role: 'system' | 'user' | 'assistant';
	content: string;
}

export interface AIResponse {
	content: string;
	model: string;
	usage?: {
		promptTokens: number;
		completionTokens: number;
		totalTokens: number;
	};
	error?: string;
}

export interface StreamResponse {
	content: string;
	done: boolean;
	error?: string;
}

export abstract class AI<PERSON>rovider {
	protected apiKey: string;
	protected baseUrl: string;

	constructor(apiKey: string, baseUrl: string) {
		this.apiKey = apiKey;
		this.baseUrl = baseUrl;
	}

	abstract chat(messages: AIMessage[], model: string, stream?: boolean): Promise<AIResponse>;
	abstract streamChat(messages: AIMessage[], model: string): AsyncGenerator<StreamResponse, void, unknown>;
	abstract getAvailableModels(): string[];
	abstract estimateTokens(text: string): number;
	abstract validateModel(modelId: string, temperature?: number): Promise<{ valid: boolean; error?: string }>;
}

export class OpenRouterProvider extends AIProvider {
	constructor(apiKey: string) {
		super(apiKey, 'https://openrouter.ai/api/v1');
	}

	async chat(messages: AIMessage[], model: string, stream = false): Promise<AIResponse> {
		try {
			const response = await fetch(`${this.baseUrl}/chat/completions`, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${this.apiKey}`,
					'Content-Type': 'application/json',
					'HTTP-Referer': 'https://obsidian.md',
					'X-Title': 'Smart QA - Obsidian Plugin'
				},
				body: JSON.stringify({
					model: model.replace('openrouter/', ''),
					messages,
					stream
				})
			});

			if (!response.ok) {
				const error = await response.text();
				throw new Error(`OpenRouter API error: ${response.status} - ${error}`);
			}

			const data = await response.json();
			
			return {
				content: data.choices[0]?.message?.content || '',
				model: data.model,
				usage: data.usage ? {
					promptTokens: data.usage.prompt_tokens,
					completionTokens: data.usage.completion_tokens,
					totalTokens: data.usage.total_tokens
				} : undefined
			};
		} catch (error) {
			return {
				content: '',
				model,
				error: error instanceof Error ? error.message : 'Unknown error occurred'
			};
		}
	}

	async *streamChat(messages: AIMessage[], model: string): AsyncGenerator<StreamResponse, void, unknown> {
		try {
			const response = await fetch(`${this.baseUrl}/chat/completions`, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${this.apiKey}`,
					'Content-Type': 'application/json',
					'HTTP-Referer': 'https://obsidian.md',
					'X-Title': 'Smart QA - Obsidian Plugin'
				},
				body: JSON.stringify({
					model: model.replace('openrouter/', ''),
					messages,
					stream: true
				})
			});

			if (!response.ok) {
				const error = await response.text();
				yield { content: '', done: true, error: `OpenRouter API error: ${response.status} - ${error}` };
				return;
			}

			const reader = response.body?.getReader();
			if (!reader) {
				yield { content: '', done: true, error: 'No response body' };
				return;
			}

			let buffer = '';
			while (true) {
				const { done, value } = await reader.read();
				if (done) break;

				buffer += new TextDecoder().decode(value);
				const lines = buffer.split('\\n');
				buffer = lines.pop() || '';

				for (const line of lines) {
					if (line.startsWith('data: ')) {
						const data = line.slice(6);
						if (data === '[DONE]') {
							yield { content: '', done: true };
							return;
						}

						try {
							const parsed = JSON.parse(data);
							const content = parsed.choices[0]?.delta?.content || '';
							if (content) {
								yield { content, done: false };
							}
						} catch (e) {
							// 忽略解析错误的行
						}
					}
				}
			}
		} catch (error) {
			yield { content: '', done: true, error: error instanceof Error ? error.message : 'Unknown error occurred' };
		}
	}

	getAvailableModels(): string[] {
		return [
			'anthropic/claude-3.5-sonnet',
			'anthropic/claude-3-haiku',
			'openai/gpt-4o',
			'openai/gpt-4o-mini',
			'google/gemini-pro-1.5'
		];
	}

	estimateTokens(text: string): number {
		// 简单估算：平均每4个字符=1个token
		return Math.ceil(text.length / 4);
	}

	async validateModel(modelId: string, temperature = 0.7): Promise<{ valid: boolean; error?: string }> {
		try {
			const testMessages: AIMessage[] = [
				{ role: 'user', content: 'Hi' }
			];

			const response = await fetch(`${this.baseUrl}/chat/completions`, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${this.apiKey}`,
					'Content-Type': 'application/json',
					'HTTP-Referer': 'https://obsidian.md',
					'X-Title': 'Smart QA - Model Validation'
				},
				body: JSON.stringify({
					model: modelId.replace('openrouter/', ''),
					messages: testMessages,
					temperature,
					max_tokens: 10
				})
			});

			if (!response.ok) {
				const error = await response.text();
				return { valid: false, error: `API error: ${response.status} - ${error}` };
			}

			const data = await response.json();
			if (data.choices && data.choices.length > 0) {
				return { valid: true };
			} else {
				return { valid: false, error: 'No valid response received' };
			}
		} catch (error) {
			return { 
				valid: false, 
				error: error instanceof Error ? error.message : 'Validation failed' 
			};
		}
	}
}

export class GoogleAIProvider extends AIProvider {
	constructor(apiKey: string) {
		super(apiKey, 'https://generativelanguage.googleapis.com/v1beta');
	}

	async chat(messages: AIMessage[], model: string): Promise<AIResponse> {
		try {
			// 转换消息格式为 Google AI 格式
			const contents = this.convertMessagesToGoogleFormat(messages);
			
			const response = await fetch(`${this.baseUrl}/models/${model.replace('google/', '')}:generateContent?key=${this.apiKey}`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					contents,
					generationConfig: {
						temperature: 0.7,
						maxOutputTokens: 4096
					}
				})
			});

			if (!response.ok) {
				const error = await response.text();
				throw new Error(`Google AI API error: ${response.status} - ${error}`);
			}

			const data = await response.json();
			
			return {
				content: data.candidates[0]?.content?.parts[0]?.text || '',
				model,
				usage: data.usageMetadata ? {
					promptTokens: data.usageMetadata.promptTokenCount,
					completionTokens: data.usageMetadata.candidatesTokenCount,
					totalTokens: data.usageMetadata.totalTokenCount
				} : undefined
			};
		} catch (error) {
			return {
				content: '',
				model,
				error: error instanceof Error ? error.message : 'Unknown error occurred'
			};
		}
	}

	async *streamChat(messages: AIMessage[], model: string): AsyncGenerator<StreamResponse, void, unknown> {
		try {
			const contents = this.convertMessagesToGoogleFormat(messages);
			
			const response = await fetch(`${this.baseUrl}/models/${model.replace('google/', '')}:streamGenerateContent?key=${this.apiKey}`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					contents,
					generationConfig: {
						temperature: 0.7,
						maxOutputTokens: 4096
					}
				})
			});

			if (!response.ok) {
				const error = await response.text();
				yield { content: '', done: true, error: `Google AI API error: ${response.status} - ${error}` };
				return;
			}

			const reader = response.body?.getReader();
			if (!reader) {
				yield { content: '', done: true, error: 'No response body' };
				return;
			}

			let buffer = '';
			while (true) {
				const { done, value } = await reader.read();
				if (done) break;

				buffer += new TextDecoder().decode(value);
				const lines = buffer.split('\\n');
				buffer = lines.pop() || '';

				for (const line of lines) {
					if (line.trim()) {
						try {
							const data = JSON.parse(line);
							const content = data.candidates[0]?.content?.parts[0]?.text || '';
							if (content) {
								yield { content, done: false };
							}
							if (data.candidates[0]?.finishReason) {
								yield { content: '', done: true };
								return;
							}
						} catch (e) {
							// 忽略解析错误的行
						}
					}
				}
			}

			yield { content: '', done: true };
		} catch (error) {
			yield { content: '', done: true, error: error instanceof Error ? error.message : 'Unknown error occurred' };
		}
	}

	private convertMessagesToGoogleFormat(messages: AIMessage[]): any[] {
		const contents: Array<{ role: 'user' | 'model'; parts: Array<{ text: string }> }> = [];
		let systemPrompt = '';

		for (const message of messages) {
			if (message.role === 'system') {
				systemPrompt += message.content + '\n';
			} else {
				contents.push({
					role: message.role === 'user' ? 'user' : 'model',
					parts: [{ text: message.content }]
				});
			}
		}

		// 如果有系统提示，将其合并到“最后一条用户消息”，确保当前提问携带上下文
		if (systemPrompt) {
			let lastUserIndex = -1;
			for (let i = contents.length - 1; i >= 0; i--) {
				if (contents[i].role === 'user') { lastUserIndex = i; break; }
			}
			if (lastUserIndex >= 0) {
				contents[lastUserIndex].parts[0].text = systemPrompt + contents[lastUserIndex].parts[0].text;
			} else {
				// 如果历史里没有用户消息，则将系统提示作为第一条用户消息发出
				contents.unshift({ role: 'user', parts: [{ text: systemPrompt }] });
			}
		}

		return contents;
	}

	getAvailableModels(): string[] {
		return [
			'gemini-pro',
			'gemini-1.5-pro'
		];
	}

	estimateTokens(text: string): number {
		// Google AI 的 token 计算略有不同
		return Math.ceil(text.length / 3.5);
	}

	async validateModel(modelId: string, temperature = 0.7): Promise<{ valid: boolean; error?: string }> {
		try {
			const testMessages: AIMessage[] = [
				{ role: 'user', content: 'Hi' }
			];

			const contents = this.convertMessagesToGoogleFormat(testMessages);
			
			const response = await fetch(`${this.baseUrl}/models/${modelId.replace('google/', '')}:generateContent?key=${this.apiKey}`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					contents,
					generationConfig: {
						temperature,
						maxOutputTokens: 10
					}
				})
			});

			if (!response.ok) {
				const error = await response.text();
				return { valid: false, error: `Google AI API error: ${response.status} - ${error}` };
			}

			const data = await response.json();
			if (data.candidates && data.candidates.length > 0) {
				return { valid: true };
			} else {
				return { valid: false, error: 'No valid response received from Google AI' };
			}
		} catch (error) {
			return { 
				valid: false, 
				error: error instanceof Error ? error.message : 'Google AI validation failed' 
			};
		}
	}
}

export class AIService {
	private providers: Map<string, AIProvider> = new Map();

	constructor(openRouterKey?: string, googleAIKey?: string) {
		if (openRouterKey) {
			this.providers.set('openrouter', new OpenRouterProvider(openRouterKey));
		}
		if (googleAIKey) {
			this.providers.set('google', new GoogleAIProvider(googleAIKey));
		}
	}

	updateKeys(openRouterKey?: string, googleAIKey?: string) {
		this.providers.clear();
		if (openRouterKey) {
			this.providers.set('openrouter', new OpenRouterProvider(openRouterKey));
		}
		if (googleAIKey) {
			this.providers.set('google', new GoogleAIProvider(googleAIKey));
		}
	}

	async chat(messages: AIMessage[], model: string, stream = false): Promise<AIResponse> {
		const provider = this.getProvider(model);
		if (!provider) {
			return {
				content: '',
				model,
				error: `No provider available for model: ${model}`
			};
		}

		return await provider.chat(messages, model, stream);
	}

	async *streamChat(messages: AIMessage[], model: string): AsyncGenerator<StreamResponse, void, unknown> {
		const provider = this.getProvider(model);
		if (!provider) {
			yield { content: '', done: true, error: `No provider available for model: ${model}` };
			return;
		}

		yield* provider.streamChat(messages, model);
	}

	estimateTokens(text: string, model: string): number {
		const provider = this.getProvider(model);
		if (!provider) {
			return Math.ceil(text.length / 4); // 默认估算
		}
		return provider.estimateTokens(text);
	}

	getAvailableModels(): { provider: string; models: string[] }[] {
		const result = [];
		for (const [providerName, provider] of this.providers) {
			result.push({
				provider: providerName,
				models: provider.getAvailableModels()
			});
		}
		return result;
	}

	isModelAvailable(model: string): boolean {
		return this.getProvider(model) !== null;
	}

	async validateModel(modelId: string, temperature = 0.7): Promise<{ valid: boolean; error?: string }> {
		const provider = this.getProvider(modelId);
		if (!provider) {
			return { valid: false, error: `No provider available for model: ${modelId}` };
		}
		return await provider.validateModel(modelId, temperature);
	}

	private getProvider(model: string): AIProvider | null {
		if (model.startsWith('openrouter/')) {
			return this.providers.get('openrouter') || null;
		}
		if (model.startsWith('google/')) {
			return this.providers.get('google') || null;
		}
		return null;
	}
}