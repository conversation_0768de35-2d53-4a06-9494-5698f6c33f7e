export interface ModelConfig {
	id: string;
	name: string;
	provider: 'openrouter' | 'google';
	modelId: string;
	temperature: number;
}

export interface SmartQASettings {
	openRouterApiKey: string;
	googleAiApiKey: string;
	defaultModel: string;
	customModels: ModelConfig[];
	enableHybridMode: boolean;
	maxContextTokens: number;
	saveConversationsToNotes: boolean;
	notesFolder: string;
	includeSystemPrompt: boolean;
	uiFontSize: number;
}

export const DEFAULT_SETTINGS: SmartQASettings = {
	openRouterApiKey: '',
	googleAiApiKey: '',
	defaultModel: '',
	customModels: [],
	enableHybridMode: false,
	maxContextTokens: 100000,
	saveConversationsToNotes: true,
	notesFolder: 'Smart QA Conversations',
	includeSystemPrompt: false,
	uiFontSize: 14
}

// Utility functions for model management
export function addModel(settings: SmartQASettings, model: ModelConfig): void {
	settings.customModels.push(model);
}

export function removeModel(settings: SmartQASettings, modelId: string): void {
	settings.customModels = settings.customModels.filter(m => m.id !== modelId);
}

export function updateModel(settings: SmartQASettings, modelId: string, updates: Partial<ModelConfig>): void {
	const modelIndex = settings.customModels.findIndex(m => m.id === modelId);
	if (modelIndex >= 0) {
		settings.customModels[modelIndex] = { ...settings.customModels[modelIndex], ...updates };
	}
}

export function getModelInfo(settings: SmartQASettings, modelId: string): ModelConfig | undefined {
	return settings.customModels.find(model => model.id === modelId);
}

export function getModelsByProvider(settings: SmartQASettings, provider: 'openrouter' | 'google'): ModelConfig[] {
	return settings.customModels.filter(model => model.provider === provider);
}

export function generateModelId(): string {
	return 'model_' + Math.random().toString(36).substr(2, 9);
}
