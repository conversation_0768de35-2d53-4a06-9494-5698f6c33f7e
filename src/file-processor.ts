import { App, TFile, getAllTags, CachedMetadata } from 'obsidian';

export interface FileInfo {
	file: TFile;
	content: string;
	metadata?: CachedMetadata;
	tags: string[];
	wordCount: number;
}

export interface ProcessedContent {
	files: FileInfo[];
	totalWordCount: number;
	summary: string;
}

export class FileProcessor {
	private app: App;

	constructor(app: App) {
		this.app = app;
	}

	/**
	 * 根据选择模式获取文件
	 */
	async getFilesBySelection(mode: 'all' | 'folder' | 'tags', selection?: string | string[]): Promise<TFile[]> {
		const allFiles = this.app.vault.getMarkdownFiles();
		console.log(`[FileProcessor] Total markdown files: ${allFiles.length}`);
		console.log(`[FileProcessor] Selection mode: ${mode}, selection:`, selection);

		switch (mode) {
			case 'all':
				return allFiles;
			
			case 'folder':
				if (!selection || typeof selection !== 'string') {
					console.log(`[FileProcessor] Invalid folder selection`);
					return [];
				}
				// 确保文件夹路径以 '/' 结尾进行正确匹配
				const folderPath = selection.endsWith('/') ? selection : selection + '/';
				console.log(`[FileProcessor] Looking for files in folder: "${selection}"`);
				console.log(`[FileProcessor] Folder path with slash: "${folderPath}"`);
				
				// 详细日志每个文件的匹配情况
				const matchedFiles = allFiles.filter(file => {
					const pathMatch = file.path.startsWith(folderPath);
					const parentMatch = file.parent?.path === selection;
					const matches = pathMatch || parentMatch;
					
					if (matches) {
						console.log(`[FileProcessor] ✓ Matched file: ${file.path} (parent: ${file.parent?.path})`);
					}
					return matches;
				});
				
				console.log(`[FileProcessor] Found ${matchedFiles.length} files in folder "${selection}"`);
				if (matchedFiles.length === 0) {
					console.log(`[FileProcessor] Sample file paths from vault:`);
					allFiles.slice(0, 5).forEach(file => {
						console.log(`[FileProcessor]   - ${file.path} (parent: ${file.parent?.path})`);
					});
				}
				
				return matchedFiles;
			
			case 'tags':
				if (!selection || !Array.isArray(selection)) return [];
				const filesWithTags: TFile[] = [];
				
				for (const file of allFiles) {
					const metadata = this.app.metadataCache.getFileCache(file);
					if (metadata) {
						const fileTags = getAllTags(metadata) || [];
						if (selection.some(tag => fileTags.includes(tag))) {
							filesWithTags.push(file);
						}
					}
				}
				return filesWithTags;
			
			default:
				return [];
		}
	}

	/**
	 * 处理单个文件
	 */
	async processFile(file: TFile): Promise<FileInfo | null> {
		try {
			console.log(`[FileProcessor] Processing file: ${file.path}`);
			const content = await this.app.vault.read(file);
			console.log(`[FileProcessor] File content length: ${content.length}`);
			const metadata = this.app.metadataCache.getFileCache(file);
			const tags = getAllTags(metadata) || [];
			
			// 预处理内容
			const processedContent = this.preprocessContent(content);
			console.log(`[FileProcessor] Processed content length: ${processedContent.length}`);
			const wordCount = this.countWords(processedContent);
			console.log(`[FileProcessor] Word count: ${wordCount}`);

			return {
				file,
				content: processedContent,
				metadata,
				tags,
				wordCount
			};
		} catch (error) {
			console.error(`Failed to process file ${file.path}:`, error);
			return null;
		}
	}

	/**
	 * 批量处理文件
	 */
	async processFiles(files: TFile[], maxFiles = 100): Promise<ProcessedContent> {
		// 限制文件数量，避免超出上下文限制
		const filesToProcess = files.slice(0, maxFiles);
		const fileInfos: FileInfo[] = [];
		let totalWordCount = 0;

		for (const file of filesToProcess) {
			const fileInfo = await this.processFile(file);
			if (fileInfo) {
				fileInfos.push(fileInfo);
				totalWordCount += fileInfo.wordCount;
			}
		}

		// 生成摘要信息
		const summary = this.generateSummary(fileInfos);

		return {
			files: fileInfos,
			totalWordCount,
			summary
		};
	}

	/**
	 * 预处理 Markdown 内容
	 */
	private preprocessContent(content: string): string {
		// 移除过多的空行
		content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
		
		// 处理 Obsidian 特有的语法
		// 移除 YAML front matter（但保留有用信息）
		content = content.replace(/^---[\s\S]*?---\n/m, '');
		
		// 转换内部链接格式
		content = content.replace(/\[\[([^\]]+)\]\]/g, '$1');
		
		// 转换标签格式
		content = content.replace(/#([\w-]+)/g, 'tag:$1');
		
		// 清理多余的 Markdown 格式（保留重要的）
		// 保留标题、列表、粗体、斜体，但移除过于复杂的格式
		
		// 移除 HTML 标签
		content = content.replace(/<[^>]*>/g, '');
		
		// 移除过多的标点符号重复
		content = content.replace(/[!?.]{3,}/g, '...');
		
		// 统一换行符
		content = content.replace(/\r\n/g, '\n');
		
		return content.trim();
	}

	/**
	 * 统计词数
	 */
	private countWords(content: string): number {
		// 简单的词数统计
		const words = content.split(/\s+/).filter(word => word.length > 0);
		return words.length;
	}

	/**
	 * 生成内容摘要
	 */
	private generateSummary(fileInfos: FileInfo[], originalStats?: { fileCount: number; totalWords: number; folders: number; tags: number }): string {
		// 如果提供了原始统计信息，使用原始统计；否则使用当前文件信息计算
		if (originalStats) {
			return `Files: ${originalStats.fileCount} | Words: ${originalStats.totalWords.toLocaleString()} | Folders: ${originalStats.folders} | Tags: ${originalStats.tags}`;
		}

		const fileCount = fileInfos.length;
		const totalWords = fileInfos.reduce((sum, info) => sum + info.wordCount, 0);

		// 统计文件类型
		const folders = new Set(fileInfos.map(info => info.file.parent?.path || 'root'));
		const tags = new Set(fileInfos.flatMap(info => info.tags));

		// 保留核心统计信息，移除 Largest 字段
		return `Files: ${fileCount} | Words: ${totalWords.toLocaleString()} | Folders: ${folders.size} | Tags: ${tags.size}`;
	}

	/**
	 * 将处理后的内容转换为 AI 可理解的格式
	 */
	formatForAI(processedContent: ProcessedContent, includeFileNames = true): string {
		let formattedContent = '';
		
		if (includeFileNames) {
			formattedContent += `=== KNOWLEDGE BASE CONTENT (${processedContent.files.length} files) ===\n\n`;
		}

		for (const fileInfo of processedContent.files) {
			if (includeFileNames) {
				formattedContent += `## File: ${fileInfo.file.name}\n`;
				formattedContent += `Path: ${fileInfo.file.path}\n`;
				if (fileInfo.tags.length > 0) {
					formattedContent += `Tags: ${fileInfo.tags.join(', ')}\n`;
				}
				formattedContent += `\n`;
			}
			
			formattedContent += fileInfo.content;
			formattedContent += '\n\n---\n\n';
		}

		return formattedContent;
	}

	/**
	 * 获取所有可用的文件夹
	 */
	getAllFolders(): string[] {
		const folders = new Set<string>();
		const files = this.app.vault.getMarkdownFiles();
		console.log(`[FileProcessor] getAllFolders: Found ${files.length} markdown files`);
		
		for (const file of files) {
			if (file.parent && file.parent.path !== '/') {
				folders.add(file.parent.path);
				console.log(`[FileProcessor] Added folder: "${file.parent.path}" from file: ${file.path}`);
			}
		}
		
		const folderList = Array.from(folders).sort();
		console.log(`[FileProcessor] All available folders:`, folderList);
		return folderList;
	}

	/**
	 * 获取所有可用的标签
	 */
	getAllTags(): string[] {
		const tags = new Set<string>();
		const files = this.app.vault.getMarkdownFiles();
		
		for (const file of files) {
			const metadata = this.app.metadataCache.getFileCache(file);
			if (metadata) {
				const fileTags = getAllTags(metadata) || [];
				fileTags.forEach(tag => tags.add(tag));
			}
		}
		
		return Array.from(tags).sort();
	}

	/**
	 * 获取文件的完整统计信息（不处理内容）
	 */
	async getFileStats(files: TFile[]): Promise<{ fileCount: number; totalWords: number; folders: number; tags: number }> {
		const folders = new Set<string>();
		const tags = new Set<string>();
		let totalWords = 0;

		for (const file of files) {
			// 统计文件夹
			folders.add(file.parent?.path || 'root');

			// 统计标签
			const metadata = this.app.metadataCache.getFileCache(file);
			if (metadata) {
				const fileTags = getAllTags(metadata) || [];
				fileTags.forEach(tag => tags.add(tag));
			}

			// 统计字数（简单估算，不读取完整内容）
			try {
				const content = await this.app.vault.read(file);
				const processedContent = this.preprocessContent(content);
				totalWords += this.countWords(processedContent);
			} catch (error) {
				console.warn(`Failed to read file ${file.path} for stats:`, error);
			}
		}

		return {
			fileCount: files.length,
			totalWords,
			folders: folders.size,
			tags: tags.size
		};
	}

	/**
	 * 统计所有标签及其出现次数
	 */
	getTagCounts(): { tag: string; count: number }[] {
		const tagToCount = new Map<string, number>();
		const files = this.app.vault.getMarkdownFiles();
		for (const file of files) {
			const metadata = this.app.metadataCache.getFileCache(file);
			if (!metadata) continue;
			const fileTags = getAllTags(metadata) || [];
			for (const tag of fileTags) {
				tagToCount.set(tag, (tagToCount.get(tag) || 0) + 1);
			}
		}
		return Array.from(tagToCount.entries())
			.map(([tag, count]) => ({ tag, count }))
			.sort((a, b) => b.count - a.count || a.tag.localeCompare(b.tag));
	}

	/**
	 * 估算内容的 token 数量（简单估算）
	 */
	estimateTokens(content: string): number {
		// 更精确的 token 估算
		// 英文平均 4 字符 = 1 token
		// 中文平均 1.5 字符 = 1 token
		const englishChars = (content.match(/[a-zA-Z0-9\s]/g) || []).length;
		const chineseChars = (content.match(/[\u4e00-\u9fff]/g) || []).length;
		const otherChars = content.length - englishChars - chineseChars;
		
		const englishTokens = Math.ceil(englishChars / 4);
		const chineseTokens = Math.ceil(chineseChars / 1.5);
		const otherTokens = Math.ceil(otherChars / 3);
		
		return englishTokens + chineseTokens + otherTokens;
	}

	/**
	 * 智能截断内容以适应 token 限制
	 */
	truncateContent(processedContent: ProcessedContent, maxTokens: number, originalStats?: { fileCount: number; totalWords: number; folders: number; tags: number }): ProcessedContent {
		const { files } = processedContent;
		const truncatedFiles: FileInfo[] = [];
		let currentTokens = 0;
		
		// 按文件大小排序，优先保留小文件
		const sortedFiles = [...files].sort((a, b) => a.wordCount - b.wordCount);
		
		for (const fileInfo of sortedFiles) {
			const fileTokens = this.estimateTokens(fileInfo.content);
			
			if (currentTokens + fileTokens <= maxTokens) {
				truncatedFiles.push(fileInfo);
				currentTokens += fileTokens;
			} else {
				// 如果单个文件就超过限制，截断该文件
				const remainingTokens = maxTokens - currentTokens;
				if (remainingTokens > 100) { // 至少保留100个token的空间
					const truncatedContent = this.truncateText(fileInfo.content, remainingTokens);
					truncatedFiles.push({
						...fileInfo,
						content: truncatedContent + '\n\n[Content truncated...]',
						wordCount: this.countWords(truncatedContent)
					});
				}
				break;
			}
		}
		
		return {
			files: truncatedFiles,
			totalWordCount: truncatedFiles.reduce((sum, info) => sum + info.wordCount, 0),
			summary: this.generateSummary(truncatedFiles, originalStats)
		};
	}

	/**
	 * 截断单个文本到指定 token 数
	 */
	private truncateText(text: string, maxTokens: number): string {
		const estimatedCharsPerToken = 4;
		const maxChars = maxTokens * estimatedCharsPerToken;
		
		if (text.length <= maxChars) {
			return text;
		}
		
		// 在句子边界截断
		const truncated = text.substring(0, maxChars);
		const lastSentenceEnd = Math.max(
			truncated.lastIndexOf('.'),
			truncated.lastIndexOf('!'),
			truncated.lastIndexOf('?'),
			truncated.lastIndexOf('\n\n')
		);
		
		if (lastSentenceEnd > maxChars * 0.8) {
			return truncated.substring(0, lastSentenceEnd + 1);
		}
		
		return truncated;
	}
}