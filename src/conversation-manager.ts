import { App, TFile, Notice } from 'obsidian';
import { AIMessage } from './api';
import SmartQAPlugin from '../main';

export interface Conversation {
	id: string;
	title: string;
	messages: ConversationMessage[];
	createdAt: Date;
	updatedAt: Date;
	fileSelection?: {
		mode: 'all' | 'folder' | 'tags';
		selection?: string | string[];
	};
	model?: string;
}

export interface ConversationMessage {
	type: 'user' | 'assistant' | 'system';
	content: string;
	timestamp: Date;
}

export class ConversationManager {
	private app: App;
	private plugin: SmartQAPlugin;
	private conversations: Map<string, Conversation> = new Map();

	constructor(app: App, plugin: SmartQAPlugin) {
		this.app = app;
		this.plugin = plugin;
	}

	/**
	 * 创建新对话
	 */
	createConversation(): string {
		const id = this.generateId();
		const conversation: Conversation = {
			id,
			title: 'New Conversation',
			messages: [],
			createdAt: new Date(),
			updatedAt: new Date()
		};
		
		this.conversations.set(id, conversation);
		this.saveConversations();
		return id;
	}

	/**
	 * 获取对话
	 */
	getConversation(id: string): Conversation | undefined {
		return this.conversations.get(id);
	}

	/**
	 * 更新对话
	 */
	updateConversation(id: string, updates: Partial<Conversation>) {
		const conversation = this.conversations.get(id);
		if (conversation) {
			Object.assign(conversation, updates, { updatedAt: new Date() });
			this.saveConversations();
		}
	}

	/**
	 * 添加消息到对话
	 */
	addMessage(conversationId: string, message: ConversationMessage) {
		const conversation = this.conversations.get(conversationId);
		if (conversation) {
			conversation.messages.push(message);
			conversation.updatedAt = new Date();
			
			// 自动生成标题（基于第一个用户消息）
			if (conversation.title === 'New Conversation' && message.type === 'user' && message.content.trim()) {
				conversation.title = this.generateTitle(message.content);
			}
			
			this.saveConversations();
		}
	}

	/**
	 * 删除对话
	 */
	deleteConversation(id: string) {
		this.conversations.delete(id);
		this.saveConversations();
	}

	/**
	 * 获取所有对话
	 */
	getAllConversations(): Conversation[] {
		return Array.from(this.conversations.values())
			.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
	}

	/**
	 * 将对话转换为 AI 消息格式
	 */
	conversationToAIMessages(conversation: Conversation): AIMessage[] {
		return conversation.messages
			.filter(msg => msg.type !== 'system')
			.map(msg => ({
				role: msg.type as 'user' | 'assistant',
				content: msg.content
			}));
	}

	/**
	 * 从 AI 消息转换为对话消息
	 */
	aiMessageToConversationMessage(message: AIMessage): ConversationMessage {
		return {
			type: message.role === 'system' ? 'system' : message.role,
			content: message.content,
			timestamp: new Date()
		};
	}

	/**
	 * 生成笔记内容
	 */
	async generateNote(conversationId: string): Promise<{ title: string; content: string }> {
		const conversation = this.conversations.get(conversationId);
		if (!conversation) {
			throw new Error('Conversation not found');
		}

		const title = `Smart QA - ${conversation.title}`;
		let content = '';

		// 添加元数据
		content += `# ${title}\n\n`;
		content += `**Created:** ${conversation.createdAt.toLocaleString()}\n`;
		content += `**Updated:** ${conversation.updatedAt.toLocaleString()}\n`;
		
		if (conversation.model) {
			content += `**Model:** ${conversation.model}\n`;
		}
		
		if (conversation.fileSelection) {
			let selectionText = '';
			switch (conversation.fileSelection.mode) {
				case 'all':
					selectionText = 'All files in vault';
					break;
				case 'folder':
					selectionText = `Folder: ${conversation.fileSelection.selection}`;
					break;
				case 'tags':
					selectionText = `Tags: ${Array.isArray(conversation.fileSelection.selection) ? conversation.fileSelection.selection.join(', ') : conversation.fileSelection.selection}`;
					break;
			}
			content += `**File Selection:** ${selectionText}\n`;
		}

		content += '\n---\n\n';

		// 添加对话内容
		for (const message of conversation.messages) {
			if (message.type === 'system') continue; // 跳过系统消息
			
			const timestamp = message.timestamp.toLocaleTimeString();
			const role = message.type === 'user' ? '🧑 User' : '🤖 Assistant';
			
			content += `## ${role} (${timestamp})\n\n`;
			content += `${message.content}\n\n`;
		}

		// 添加脚注
		content += '\n---\n\n';
		content += '*Generated by Smart QA plugin for Obsidian*\n';

		return { title, content };
	}

	/**
	 * 保存笔记到 vault
	 */
	async saveAsNote(conversationId: string): Promise<TFile> {
		const { title, content } = await this.generateNote(conversationId);
		
		// 获取笔记保存文件夹
		const folderPath = this.plugin.settings.notesFolder || 'Smart QA Conversations';
		
		// 确保文件夹存在
		const folder = this.app.vault.getAbstractFileByPath(folderPath);
		if (!folder) {
			await this.app.vault.createFolder(folderPath);
		}

		// 生成唯一文件名
		const sanitizedTitle = title.replace(/[<>:"/\\|?*]/g, '-');
		const fileName = `${sanitizedTitle}.md`;
		const filePath = `${folderPath}/${fileName}`;
		
		// 检查文件是否已存在
		let finalPath = filePath;
		let counter = 1;
		while (this.app.vault.getAbstractFileByPath(finalPath)) {
			finalPath = `${folderPath}/${sanitizedTitle} (${counter}).md`;
			counter++;
		}

		// 创建文件
		const file = await this.app.vault.create(finalPath, content);
		
		new Notice(`Conversation saved as note: ${file.name}`);
		return file;
	}

	/**
	 * 加载保存的对话
	 */
	async loadConversations() {
		try {
			const data = await this.plugin.loadData();
			if (data && data.conversations) {
				this.conversations.clear();
				
				for (const convData of data.conversations) {
					const conversation: Conversation = {
						...convData,
						createdAt: new Date(convData.createdAt),
						updatedAt: new Date(convData.updatedAt),
						messages: convData.messages.map((msg: any) => ({
							...msg,
							timestamp: new Date(msg.timestamp)
						}))
					};
					this.conversations.set(conversation.id, conversation);
				}
			}
		} catch (error) {
			console.error('Failed to load conversations:', error);
		}
	}

	/**
	 * 保存对话到数据文件
	 */
	async saveConversations() {
		try {
			const existingData = await this.plugin.loadData() || {};
			const conversationsData = Array.from(this.conversations.values());
			
			await this.plugin.saveData({
				...existingData,
				conversations: conversationsData
			});
		} catch (error) {
			console.error('Failed to save conversations:', error);
		}
	}

	/**
	 * 清理旧对话（保留最近的100个）
	 */
	cleanup() {
		const conversations = this.getAllConversations();
		const maxConversations = 100;
		
		if (conversations.length > maxConversations) {
			const toDelete = conversations.slice(maxConversations);
			for (const conv of toDelete) {
				this.conversations.delete(conv.id);
			}
			this.saveConversations();
		}
	}

	/**
	 * 生成对话ID
	 */
	private generateId(): string {
		return Date.now().toString(36) + Math.random().toString(36).substr(2);
	}

	/**
	 * 从用户消息生成标题
	 */
	private generateTitle(content: string): string {
		// 取前50个字符作为标题，并清理格式
		let title = content.trim().substring(0, 50);
		title = title.replace(/\n/g, ' ').replace(/\s+/g, ' ');
		
		if (content.length > 50) {
			title += '...';
		}
		
		return title;
	}
}