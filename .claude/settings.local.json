{"permissions": {"allow": ["mcp__sequential-thinking__sequentialthinking", "Bash(npm install)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "<PERSON><PERSON>(pkill:*)", "Bash(node server.js)", "Bash(npm start)", "<PERSON><PERSON>(true)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_handle_dialog", "mcp__playwright__browser_type", "mcp__playwright__browser_press_key", "mcp__playwright__browser_evaluate", "Bash(npm install:*)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(npx playwright install:*)", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_close", "mcp__playwright__browser_click"], "deny": [], "ask": []}}