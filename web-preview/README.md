# Smart QA Web Preview

这是 Smart QA Obsidian 插件的网页版预览，用于快速开发和调试。

## 🚀 快速开始

### 1. 启动开发服务器

```bash
# 进入 web-preview 目录
cd web-preview

# 启动服务器
npm start
# 或者
node server.js
```

### 2. 访问应用

打开浏览器访问：http://localhost:3000

## 📋 功能特性

### ✅ 已实现功能

- **完整的 UI 界面**：与 Obsidian 插件相同的 Notion 风格设计
- **文件选择系统**：支持全部文件、文件夹、标签三种模式
- **AI 对话功能**：支持 OpenRouter 和 Google AI 集成
- **流式响应**：实时显示 AI 回复
- **示例数据**：内置 9 个示例 markdown 文件
- **模型管理**：添加、删除、设置默认模型
- **对话导出**：保存对话记录为 markdown 文件
- **响应式设计**：适配不同屏幕尺寸

### 🎯 主要组件

1. **左侧配置面板**
   - API 密钥配置
   - 模型管理
   - 连接测试

2. **右侧主界面**
   - 文件选择区域
   - 消息对话区域
   - 输入工具栏
   - 状态栏

## 🔧 配置说明

### API 密钥

在左侧配置面板中设置：

- **OpenRouter API Key**：支持 Claude、GPT-4 等模型
- **Google AI API Key**：支持 Gemini 模型

### 模型配置

支持的模型示例：

| 提供商 | 模型 ID | 说明 |
|--------|---------|------|
| OpenRouter | `anthropic/claude-3.5-sonnet` | Claude 3.5 Sonnet |
| OpenRouter | `openai/gpt-4o` | GPT-4o |
| Google | `gemini-1.5-pro` | Gemini 1.5 Pro |

## 🗂️ 示例数据

内置的示例文件包括：

- `README.md` - 知识库总览
- `programming/JavaScript基础.md` - 编程笔记
- `programming/React开发实践.md` - 框架学习
- `projects/待办事项应用.md` - 项目记录
- `learning/TypeScript学习.md` - 学习笔记
- `ideas/知识管理系统构想.md` - 创意想法
- `daily/2024-01-15.md` - 日记记录
- `resources/编程学习资源.md` - 资源汇总
- `work/项目复盘-电商系统.md` - 工作复盘

## 🛠️ 开发说明

### 项目结构

```
web-preview/
├── index.html              # 主页面
├── assets/
│   ├── css/
│   │   └── smart-qa.css    # 样式文件
│   └── js/
│       ├── main.js         # 主应用逻辑
│       ├── obsidian-mock.js # Obsidian API 模拟
│       ├── api-service.js  # AI 服务层
│       ├── file-processor.js # 文件处理器
│       ├── ui-controller.js # UI 控制器
│       └── mock-data.js    # 模拟数据
├── server.js               # 本地开发服务器
├── package.json            # 项目配置
└── README.md              # 说明文档
```

### 技术栈

- **前端**：原生 JavaScript (ES6 模块)
- **样式**：CSS3 + CSS 变量
- **服务器**：Node.js HTTP 服务器
- **API**：OpenRouter + Google AI

### 核心特性

1. **模块化设计**：每个功能模块独立，易于维护
2. **Mock 系统**：完整模拟 Obsidian API，无需依赖
3. **响应式 UI**：支持桌面和移动设备
4. **实时预览**：修改代码后刷新即可看到效果

## 🔄 开发流程

1. **在网页版中调试功能**
2. **验证 UI 交互和 API 集成**
3. **将改进的代码移植回 Obsidian 插件**

## 📝 使用说明

### 第一次使用

1. 启动服务器：`npm start`
2. 打开 http://localhost:3000
3. 在左侧配置 API 密钥
4. 添加 AI 模型
5. 开始对话测试

### 测试文件选择

1. **All Files**：包含所有示例文件
2. **Folder**：选择 `programming` 文件夹
3. **Tags**：选择 `#编程` 或 `#项目` 标签

### 测试 AI 对话

示例问题：
- "总结一下我的编程学习笔记"
- "我有哪些项目经验？"
- "关于 React 开发，你有什么建议？"

## 🐛 故障排除

### 常见问题

1. **API 调用失败**
   - 检查 API 密钥是否正确
   - 确认网络连接正常
   - 查看浏览器控制台错误信息

2. **界面显示异常**
   - 刷新浏览器页面
   - 检查浏览器控制台是否有 JavaScript 错误

3. **端口占用**
   - 使用不同端口：`PORT=3001 npm start`

### 调试工具

- 浏览器开发者工具（F12）
- 网络面板查看 API 请求
- 控制台查看日志信息

## 🎯 下一步

完成网页版调试后，将改进的功能移植回 Obsidian 插件：

1. 复制修复的代码
2. 适配 TypeScript 类型
3. 测试插件功能
4. 发布更新版本

---

**Happy Coding! 🚀**