{"name": "smart-qa-web-preview", "version": "1.0.0", "description": "Web preview version of Smart QA Obsidian plugin for development and testing", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "serve": "node server.js"}, "keywords": ["smart-qa", "obsidian", "ai", "preview", "development"], "author": "Smart QA Team", "license": "MIT", "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "."}}