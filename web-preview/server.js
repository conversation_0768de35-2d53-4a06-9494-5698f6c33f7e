// Simple HTTP Server for Smart QA Web Preview
// 提供本地开发服务器，避免 CORS 问题

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = process.env.PORT || 3000;
const HOST = 'localhost';

// MIME types
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'application/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
};

function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

function serveFile(filePath, response) {
    fs.readFile(filePath, (err, data) => {
        if (err) {
            if (err.code === 'ENOENT') {
                response.writeHead(404, { 'Content-Type': 'text/plain' });
                response.end('File not found');
            } else {
                response.writeHead(500, { 'Content-Type': 'text/plain' });
                response.end('Server error');
            }
            return;
        }

        const mimeType = getMimeType(filePath);
        response.writeHead(200, { 
            'Content-Type': mimeType,
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        });
        response.end(data);
    });
}

const server = http.createServer((request, response) => {
    const parsedUrl = url.parse(request.url, true);
    let pathname = parsedUrl.pathname;
    
    // 处理根路径
    if (pathname === '/') {
        pathname = '/index.html';
    }
    
    // 构建文件路径
    const filePath = path.join(__dirname, pathname);
    
    // 安全检查：确保文件在当前目录下
    const normalizedPath = path.normalize(filePath);
    if (!normalizedPath.startsWith(__dirname)) {
        response.writeHead(403, { 'Content-Type': 'text/plain' });
        response.end('Forbidden');
        return;
    }
    
    // 检查文件是否存在
    fs.stat(normalizedPath, (err, stats) => {
        if (err || !stats.isFile()) {
            // 如果是 SPA 路由，返回 index.html
            if (!path.extname(pathname)) {
                const indexPath = path.join(__dirname, 'index.html');
                serveFile(indexPath, response);
            } else {
                response.writeHead(404, { 'Content-Type': 'text/plain' });
                response.end('File not found');
            }
            return;
        }
        
        serveFile(normalizedPath, response);
    });
});

server.listen(PORT, HOST, () => {
    console.log('🚀 Smart QA Web Preview Server');
    console.log(`📍 Running at: http://${HOST}:${PORT}`);
    console.log('📁 Serving files from:', __dirname);
    console.log('');
    console.log('💡 Features:');
    console.log('  • Hot reload: Refresh browser to see changes');
    console.log('  • CORS enabled: API calls should work');
    console.log('  • Static files: HTML, CSS, JS, images');
    console.log('');
    console.log('🛑 Press Ctrl+C to stop the server');
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
        console.log('💡 Try a different port:');
        console.log(`   PORT=3001 node server.js`);
    } else {
        console.error('❌ Server error:', err);
    }
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});