<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart QA - Web Preview</title>
    <link rel="stylesheet" href="assets/css/smart-qa.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
            background: var(--background-primary);
        }
        
        .sidebar {
            width: 300px;
            border-right: 1px solid var(--background-modifier-border);
            background: var(--background-secondary);
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .logo {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-normal);
            margin-bottom: 20px;
        }
        
        .config-section {
            background: var(--background-primary);
            padding: 16px;
            border-radius: 8px;
            border: 1px solid var(--background-modifier-border);
        }
        
        .config-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 12px;
            color: var(--text-normal);
        }
        
        .form-group {
            margin-bottom: 12px;
        }
        
        .form-label {
            display: block;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 4px;
            color: var(--text-normal);
        }
        
        .form-input {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid var(--background-modifier-border);
            border-radius: 6px;
            background: var(--background-primary);
            color: var(--text-normal);
            font-size: 13px;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--interactive-accent);
            box-shadow: 0 0 0 1px var(--interactive-accent);
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: var(--interactive-accent);
            color: var(--text-on-accent);
        }
        
        .btn-primary:hover {
            opacity: 0.9;
        }
        
        .btn-secondary {
            background: var(--background-secondary);
            color: var(--text-normal);
            border: 1px solid var(--background-modifier-border);
        }
        
        .btn-secondary:hover {
            background: var(--background-modifier-hover);
        }
        
        .status-indicator {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            margin-top: 8px;
        }
        
        .status-success {
            background: #e6f7e6;
            color: #2d7d2d;
        }
        
        .status-error {
            background: #ffe6e6;
            color: #d32f2f;
        }
        
        .status-info {
            background: #e6f3ff;
            color: #0066cc;
        }
        
        .models-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid var(--background-modifier-border);
            border-radius: 6px;
            background: var(--background-primary);
        }
        
        .model-item {
            padding: 8px 12px;
            border-bottom: 1px solid var(--background-modifier-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .model-item:last-child {
            border-bottom: none;
        }
        
        .model-name {
            font-size: 12px;
            color: var(--text-normal);
        }
        
        .model-provider {
            font-size: 10px;
            color: var(--text-muted);
            text-transform: uppercase;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 11px;
        }
        
        .selected-folder-info {
            background: var(--background-primary);
            border: 1px solid var(--background-modifier-border);
            border-radius: 6px;
            padding: 12px;
            margin-top: 8px;
        }
        
        .folder-path {
            font-size: 12px;
            color: var(--text-normal);
            font-weight: 500;
            margin-bottom: 6px;
            word-break: break-all;
        }
        
        .folder-stats {
            font-size: 11px;
            color: var(--text-muted);
        }
        
        /* 文件夹选择提示动画 */
        .folder-hint {
            animation: folder-pulse 2s ease-in-out 3;
        }
        
        @keyframes folder-pulse {
            0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4); }
            50% { box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.1); }
            100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 左侧配置面板 -->
        <div class="sidebar">
            <div class="logo">🤖 Smart QA</div>
            
            <!-- API 配置 -->
            <div class="config-section">
                <div class="config-title">API 配置</div>
                
                <div class="form-group">
                    <label class="form-label" for="openrouter-key">OpenRouter API Key</label>
                    <input type="password" id="openrouter-key" class="form-input" placeholder="sk-or-...">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="google-key">Google AI API Key</label>
                    <input type="password" id="google-key" class="form-input" placeholder="AIza...">
                </div>
                
                <button id="save-config" class="btn btn-primary" style="width: 100%;">保存配置</button>
                <div id="config-status" class="status-indicator" style="display: none;"></div>
            </div>
            
            <!-- 模型管理 -->
            <div class="config-section">
                <div class="config-title">模型管理</div>
                
                <div class="form-group">
                    <label class="form-label" for="model-name">模型名称</label>
                    <input type="text" id="model-name" class="form-input" placeholder="Claude 3.5 Sonnet">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="model-provider">提供商</label>
                    <select id="model-provider" class="form-input">
                        <option value="openrouter">OpenRouter</option>
                        <option value="google">Google AI</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="model-id">模型ID</label>
                    <input type="text" id="model-id" class="form-input" placeholder="anthropic/claude-3.5-sonnet">
                </div>
                
                <button id="add-model" class="btn btn-secondary" style="width: 100%;">添加模型</button>
                
                <div class="models-list" id="models-list" style="margin-top: 12px;">
                    <!-- 动态添加模型项 -->
                </div>
            </div>
            
            <!-- 界面设置 -->
            <div class="config-section">
                <div class="config-title">界面设置</div>
                
                <div class="form-group">
                    <label class="form-label" for="font-size">字号 (10-28px)</label>
                    <input type="range" id="font-size" class="form-input" min="10" max="28" value="14" step="1">
                    <div style="font-size: 11px; color: var(--text-muted); margin-top: 4px;">
                        当前: <span id="font-size-value">14</span>px
                    </div>
                </div>
            </div>
            
            <!-- 文件夹配置 -->
            <div class="config-section">
                <div class="config-title">文件夹设置</div>
                
                <div class="form-group">
                    <label class="form-label">选择知识库文件夹</label>
                    <input type="file" id="folder-input" webkitdirectory directory multiple style="display: none;">
                    <button id="select-folder" class="btn btn-secondary" style="width: 100%;">选择文件夹</button>
                </div>
                
                <div class="form-group">
                    <div id="selected-folder-display" class="selected-folder-info" style="display: none;">
                        <div class="folder-path"></div>
                        <div class="folder-stats"></div>
                    </div>
                </div>
                
                <button id="clear-folder" class="btn btn-secondary" style="width: 100%; display: none;">清除选择</button>
            </div>
            
            <!-- 测试按钮 -->
            <div class="config-section">
                <button id="test-api" class="btn btn-primary" style="width: 100%;">测试 API 连接</button>
                <div id="test-status" class="status-indicator" style="display: none;"></div>

                <button id="debug-folder" class="btn btn-secondary" style="width: 100%; margin-top: 8px;">🔍 调试文件夹选择</button>
                <button id="test-file-processing" class="btn btn-secondary" style="width: 100%; margin-top: 4px;">📄 测试文件处理</button>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <div id="smart-qa-root" class="smart-qa-container">
                <!-- Smart QA 界面将在这里渲染 -->
            </div>
        </div>
    </div>

    <!-- JavaScript 模块 -->
    <script type="module" src="assets/js/obsidian-mock.js"></script>
    <script type="module" src="assets/js/api-service.js"></script>
    <script type="module" src="assets/js/file-processor.js"></script>
    <script type="module" src="assets/js/mock-data.js"></script>
    <script type="module" src="assets/js/ui-controller.js"></script>
    <script type="module" src="assets/js/main.js"></script>
</body>
</html>