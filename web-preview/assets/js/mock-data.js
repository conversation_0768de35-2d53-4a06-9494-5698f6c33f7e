// Mock Data for Smart QA Preview
// 提供示例 markdown 文件和设置数据

export const mockFiles = [
    {
        path: 'README.md',
        content: `# 我的知识库

欢迎来到我的个人知识库！这里记录了我的学习笔记、项目经验和思考心得。

## 主要内容

- 📚 学习笔记
- 💻 编程技术
- 🎯 项目管理
- 💡 创意想法

#知识管理 #个人成长

---

最后更新：2024年1月15日`
    },
    {
        path: 'programming/JavaScript基础.md',
        content: `# JavaScript 基础知识

## 变量声明

### let 和 const
\`\`\`javascript
let name = "张三";
const age = 25;
\`\`\`

### 作用域
- 块级作用域
- 函数作用域
- 全局作用域

## 数据类型

1. **原始类型**
   - Number
   - String
   - Boolean
   - Undefined
   - Null
   - Symbol

2. **引用类型**
   - Object
   - Array
   - Function

## 常用方法

### 数组方法
\`\`\`javascript
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
const evens = numbers.filter(n => n % 2 === 0);
\`\`\`

#编程 #JavaScript #前端

---

学习进度：✅ 已完成基础部分`
    },
    {
        path: 'programming/React开发实践.md',
        content: `# React 开发实践

## 组件设计原则

### 单一职责
每个组件应该只负责一个功能，保持简洁和可维护性。

### 组合优于继承
\`\`\`jsx
// 好的做法
function Card({ children, title }) {
  return (
    <div className="card">
      <h2>{title}</h2>
      {children}
    </div>
  );
}

function UserCard({ user }) {
  return (
    <Card title="用户信息">
      <p>姓名：{user.name}</p>
      <p>邮箱：{user.email}</p>
    </Card>
  );
}
\`\`\`

## 状态管理

### useState Hook
\`\`\`jsx
const [count, setCount] = useState(0);

const increment = () => setCount(prev => prev + 1);
\`\`\`

### useEffect Hook
\`\`\`jsx
useEffect(() => {
  document.title = \`Count: \${count}\`;
}, [count]);
\`\`\`

## 性能优化

1. **使用 React.memo**
2. **useCallback 和 useMemo**
3. **代码分割和懒加载**

#编程 #React #前端 #JavaScript

---

实战项目：[[待办事项应用]] | [[博客系统]]`
    },
    {
        path: 'projects/待办事项应用.md',
        content: `# 待办事项应用开发

## 项目概述

一个基于 React 的现代化待办事项管理应用，支持任务分类、优先级设置和进度跟踪。

## 技术栈

- **前端**：React 18 + TypeScript
- **状态管理**：Zustand
- **样式**：Tailwind CSS
- **构建工具**：Vite

## 核心功能

### 任务管理
- ✅ 创建任务
- ✅ 编辑任务
- ✅ 删除任务
- ✅ 标记完成

### 分类系统
- 工作任务
- 个人事务
- 学习计划
- 购物清单

### 优先级
- 🔴 高优先级
- 🟡 中优先级
- 🟢 低优先级

## 开发进度

- [x] 项目初始化
- [x] 基础组件开发
- [x] 状态管理实现
- [ ] 数据持久化
- [ ] 用户界面优化
- [ ] 部署上线

## 学到的经验

1. **组件设计**：保持组件的单一职责和可复用性
2. **状态管理**：合理使用本地状态和全局状态
3. **用户体验**：注重交互反馈和加载状态

#项目 #React #编程 #待办事项

---

相关笔记：[[React开发实践]] | [[TypeScript学习]]`
    },
    {
        path: 'learning/TypeScript学习.md',
        content: `# TypeScript 学习笔记

## 为什么使用 TypeScript？

1. **类型安全**：编译时发现错误
2. **更好的IDE支持**：自动补全、重构
3. **代码文档化**：类型即文档
4. **团队协作**：统一的代码规范

## 基础类型

### 原始类型
\`\`\`typescript
let name: string = "张三";
let age: number = 25;
let isStudent: boolean = true;
\`\`\`

### 数组和对象
\`\`\`typescript
let numbers: number[] = [1, 2, 3];
let user: { name: string; age: number } = {
  name: "李四",
  age: 30
};
\`\`\`

## 接口和类型别名

### 接口定义
\`\`\`typescript
interface User {
  id: number;
  name: string;
  email?: string; // 可选属性
  readonly createdAt: Date; // 只读属性
}
\`\`\`

### 类型别名
\`\`\`typescript
type Status = "pending" | "completed" | "cancelled";
type UserWithStatus = User & { status: Status };
\`\`\`

## 泛型

\`\`\`typescript
function identity<T>(arg: T): T {
  return arg;
}

interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}
\`\`\`

## 实用工具类型

\`\`\`typescript
// Partial - 所有属性变为可选
type PartialUser = Partial<User>;

// Pick - 选择特定属性
type UserSummary = Pick<User, 'id' | 'name'>;

// Omit - 排除特定属性
type CreateUserRequest = Omit<User, 'id' | 'createdAt'>;
\`\`\`

#编程 #TypeScript #类型系统 #前端

---

进度：📖 正在学习高级类型`
    },
    {
        path: 'ideas/知识管理系统构想.md',
        content: `# 知识管理系统构想

## 核心理念

建立一个**个人知识网络**，将零散的信息转化为有价值的知识资产。

## 系统架构

### 1. 输入层
- 📝 笔记记录
- 📚 文档整理
- 🔗 链接收藏
- 💭 想法捕获

### 2. 处理层
- 🏷️ 自动标签
- 🔍 智能搜索
- 📊 关系图谱
- 🤖 AI 助手

### 3. 输出层
- 📄 知识报告
- 💡 创意激发
- 🎯 决策支持
- 📈 学习追踪

## 技术选型

### 前端
- **框架**：React + TypeScript
- **状态管理**：Zustand
- **图形化**：D3.js / Cytoscape.js
- **编辑器**：Monaco Editor

### 后端
- **API**：Node.js + Express
- **数据库**：PostgreSQL + Redis
- **搜索**：Elasticsearch
- **AI**：OpenAI API

### 特色功能

1. **双向链接**：类似 Obsidian 的链接系统
2. **智能标签**：AI 自动提取和推荐标签
3. **知识图谱**：可视化知识之间的关系
4. **智能问答**：基于个人知识库的 AI 助手

## 实现计划

### Phase 1: MVP (最小可行产品)
- [ ] 基础笔记功能
- [ ] 标签系统
- [ ] 简单搜索

### Phase 2: 增强功能
- [ ] 双向链接
- [ ] 知识图谱
- [ ] 高级搜索

### Phase 3: AI 集成
- [ ] 智能标签
- [ ] 知识问答
- [ ] 内容推荐

#想法 #知识管理 #产品设计 #AI

---

灵感来源：Obsidian, Notion, RemNote`
    },
    {
        path: 'daily/2024-01-15.md',
        content: `# 2024年1月15日

## 今日计划

- [x] 完成 React 项目的用户认证模块
- [x] 整理 TypeScript 学习笔记
- [ ] 阅读《代码整洁之道》第3-4章
- [ ] 健身：跑步 5km

## 工作记录

### 上午 (9:00-12:00)
实现了用户登录和注册功能，使用 JWT 进行身份验证。遇到的问题：

1. **跨域问题**：在开发环境下 cookie 无法正确设置
   - 解决方案：配置 CORS 允许凭证传递

2. **密码加密**：使用 bcrypt 进行密码哈希
   \`\`\`javascript
   const hashedPassword = await bcrypt.hash(password, 10);
   \`\`\`

### 下午 (14:00-17:00)
整理了 TypeScript 的学习笔记，重点关注：
- 泛型的高级用法
- 条件类型
- 映射类型

## 今日思考

**关于代码质量**：
写代码不仅是实现功能，更重要的是让代码易读、易维护。好的代码应该像散文一样流畅。

## 明日计划

1. 优化用户界面的响应式设计
2. 添加单元测试
3. 研究 React Server Components

#日记 #工作记录 #学习日志

---

心情：😊 充实的一天`
    },
    {
        path: 'resources/编程学习资源.md',
        content: `# 编程学习资源汇总

## 在线课程平台

### 综合性平台
- **[慕课网](https://www.imooc.com/)**：中文技术课程
- **[极客时间](https://time.geekbang.org/)**：深度技术专栏
- **[Coursera](https://www.coursera.org/)**：大学级别课程
- **[Udemy](https://www.udemy.com/)**：实用技能课程

### 编程专项
- **[FreeCodeCamp](https://www.freecodecamp.org/)**：免费全栈课程
- **[LeetCode](https://leetcode.com/)**：算法题练习
- **[Codecademy](https://www.codecademy.com/)**：交互式编程学习

## 技术博客

### 个人博客
- **[阮一峰的网络日志](http://www.ruanyifeng.com/blog/)**：前端技术
- **[廖雪峰的官方网站](https://www.liaoxuefeng.com/)**：Python、Git教程
- **[张鑫旭的博客](https://www.zhangxinxu.com/)**：CSS 深度技术

### 团队博客
- **[美团技术团队](https://tech.meituan.com/)**：大厂技术实践
- **[腾讯云开发者社区](https://cloud.tencent.com/developer)**：云技术分享

## 开源项目

### 学习型项目
- **[30-seconds-of-code](https://github.com/30-seconds/30-seconds-of-code)**：JavaScript 代码片段
- **[clean-code-javascript](https://github.com/ryanmcdermott/clean-code-javascript)**：代码整洁之道
- **[javascript-algorithms](https://github.com/trekhleb/javascript-algorithms)**：算法和数据结构

### 实战项目
- **[React 官方示例](https://github.com/facebook/react/tree/main/packages/react-dom/src/__tests__/fixtures)**
- **[Node.js 最佳实践](https://github.com/goldbergyoni/nodebestpractices)**

## 技术书籍

### 基础必读
- 📖 《JavaScript 高级程序设计》
- 📖 《你不知道的 JavaScript》
- 📖 《深入理解计算机系统》
- 📖 《代码整洁之道》

### 进阶推荐
- 📖 《设计模式：可复用面向对象软件的基础》
- 📖 《重构：改善既有代码的设计》
- 📖 《系统设计面试》

## 工具推荐

### 开发环境
- **IDE**：VS Code, WebStorm
- **版本控制**：Git + GitHub
- **包管理**：npm, yarn, pnpm

### 调试工具
- **浏览器**：Chrome DevTools
- **网络**：Postman, Insomnia
- **性能**：Lighthouse, WebPageTest

#资源 #编程 #学习 #工具

---

持续更新中...`
    },
    {
        path: 'work/项目复盘-电商系统.md',
        content: `# 电商系统项目复盘

## 项目背景

**项目周期**：2023年8月 - 2023年12月  
**团队规模**：5人（2前端 + 2后端 + 1测试）  
**技术栈**：React + Node.js + PostgreSQL

## 项目目标

1. 构建一个中小型电商平台
2. 支持商品管理、订单处理、支付集成
3. 响应式设计，支持移动端
4. 日处理订单量 > 1000

## 技术架构

### 前端架构
\`\`\`
React 18
├── 状态管理: Redux Toolkit
├── 路由: React Router v6
├── UI框架: Ant Design
├── 样式: Styled Components
└── 构建: Webpack 5
\`\`\`

### 后端架构
\`\`\`
Node.js + Express
├── ORM: Prisma
├── 数据库: PostgreSQL
├── 缓存: Redis
├── 认证: JWT
└── 支付: Stripe API
\`\`\`

## 实现亮点

### 1. 性能优化
- **代码分割**：减少首屏加载时间 40%
- **图片优化**：WebP 格式 + CDN，节省 60% 带宽
- **缓存策略**：Redis 缓存热门商品，响应时间 < 100ms

### 2. 用户体验
- **搜索功能**：Elasticsearch 实现毫秒级搜索
- **实时通知**：WebSocket 推送订单状态
- **离线支持**：PWA 实现基本离线浏览

### 3. 业务功能
- **购物车**：支持多规格商品
- **优惠券**：灵活的促销规则引擎
- **库存管理**：防止超卖的分布式锁

## 遇到的挑战

### 技术挑战

1. **并发处理**
   - 问题：双11期间服务器压力过大
   - 解决：实现限流、熔断机制

2. **数据一致性**
   - 问题：支付成功但库存扣减失败
   - 解决：引入分布式事务（Saga模式）

3. **搜索性能**
   - 问题：商品数量增长后搜索变慢
   - 解决：Elasticsearch + 索引优化

### 团队协作

1. **代码规范**：ESLint + Prettier 统一代码风格
2. **Git 工作流**：GitFlow 分支管理
3. **Code Review**：至少1人 review 才能合并

## 项目成果

### 业务指标
- ✅ 日订单量峰值 1,500+
- ✅ 用户转化率 3.2%
- ✅ 系统可用性 99.9%
- ✅ 平均响应时间 < 200ms

### 技术指标
- ✅ 代码覆盖率 85%+
- ✅ 构建时间 < 3分钟
- ✅ 零安全漏洞
- ✅ SEO 评分 95+

## 经验教训

### 做得好的地方
1. **架构设计**：模块化设计，易于扩展
2. **测试覆盖**：单元测试 + 集成测试，保证质量
3. **监控体系**：完善的日志和监控

### 需要改进
1. **文档**：技术文档不够详细
2. **性能监控**：缺少前端性能监控
3. **用户反馈**：收集用户反馈的机制不完善

## 下一步优化

1. **微服务化**：将单体应用拆分为微服务
2. **AI推荐**：引入机器学习推荐系统
3. **国际化**：支持多语言和多货币

#项目 #复盘 #电商 #Node.js #React

---

总体评价：成功的项目，达到了预期目标 ⭐⭐⭐⭐⭐`
    }
];

export const mockSettings = {
    openRouterApiKey: '',
    googleAiApiKey: '',
    defaultModel: '',
    customModels: [
        {
            id: 'model_claude35sonnet',
            name: 'Claude 3.5 Sonnet',
            provider: 'openrouter',
            modelId: 'anthropic/claude-3.5-sonnet',
            temperature: 0.7
        },
        {
            id: 'model_gpt4o',
            name: 'GPT-4o',
            provider: 'openrouter', 
            modelId: 'openai/gpt-4o',
            temperature: 0.7
        },
        {
            id: 'model_gemini15pro',
            name: 'Gemini 1.5 Pro',
            provider: 'google',
            modelId: 'gemini-1.5-pro',
            temperature: 0.7
        }
    ],
    enableHybridMode: false,
    maxContextTokens: 100000,
    saveConversationsToNotes: true,
    notesFolder: 'Smart QA Conversations',
    includeSystemPrompt: false,
    uiFontSize: 14
};

// 初始化模拟数据
export function initializeMockData() {
    const app = window.__obsidianMockApp;
    if (app) {
        // 设置文件数据
        app.vault.setFiles(mockFiles);
        console.log('[Mock Data] Initialized with', mockFiles.length, 'files');
        
        // 加载保存的设置
        const savedSettings = localStorage.getItem('smart-qa-settings');
        if (!savedSettings) {
            localStorage.setItem('smart-qa-settings', JSON.stringify(mockSettings));
            console.log('[Mock Data] Initialized default settings');
        }
    }
}

// 工具函数：获取设置
export function getSettings() {
    const saved = localStorage.getItem('smart-qa-settings');
    return saved ? JSON.parse(saved) : mockSettings;
}

// 工具函数：保存设置
export function saveSettings(settings) {
    localStorage.setItem('smart-qa-settings', JSON.stringify(settings));
    console.log('[Mock Data] Settings saved');
}

// 工具函数：生成模型ID
export function generateModelId() {
    return 'model_' + Math.random().toString(36).substr(2, 9);
}

console.log('[Mock Data] Module loaded');
