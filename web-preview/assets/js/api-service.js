// AI Service Layer for Browser Environment
// Ported from src/api.ts

export class OpenRouterProvider {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://openrouter.ai/api/v1';
    }

    async chat(messages, model, stream = false) {
        try {
            const response = await fetch(`${this.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': 'https://smart-qa-preview.local',
                    'X-Title': 'Smart QA - Web Preview'
                },
                body: JSON.stringify({
                    model: model.replace('openrouter/', ''),
                    messages,
                    stream
                })
            });

            if (!response.ok) {
                const error = await response.text();
                throw new Error(`OpenRouter API error: ${response.status} - ${error}`);
            }

            const data = await response.json();
            
            return {
                content: data.choices[0]?.message?.content || '',
                model: data.model,
                usage: data.usage ? {
                    promptTokens: data.usage.prompt_tokens,
                    completionTokens: data.usage.completion_tokens,
                    totalTokens: data.usage.total_tokens
                } : undefined
            };
        } catch (error) {
            return {
                content: '',
                model,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }

    async *streamChat(messages, model) {
        try {
            const response = await fetch(`${this.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': 'https://smart-qa-preview.local',
                    'X-Title': 'Smart QA - Web Preview'
                },
                body: JSON.stringify({
                    model: model.replace('openrouter/', ''),
                    messages,
                    stream: true
                })
            });

            if (!response.ok) {
                const error = await response.text();
                yield { content: '', done: true, error: `OpenRouter API error: ${response.status} - ${error}` };
                return;
            }

            const reader = response.body?.getReader();
            if (!reader) {
                yield { content: '', done: true, error: 'No response body' };
                return;
            }

            let buffer = '';
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += new TextDecoder().decode(value);
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') {
                            yield { content: '', done: true };
                            return;
                        }

                        try {
                            const parsed = JSON.parse(data);
                            const content = parsed.choices[0]?.delta?.content || '';
                            if (content) {
                                yield { content, done: false };
                            }
                        } catch (e) {
                            // 忽略解析错误的行
                        }
                    }
                }
            }
        } catch (error) {
            yield { content: '', done: true, error: error instanceof Error ? error.message : 'Unknown error occurred' };
        }
    }

    getAvailableModels() {
        return [
            'anthropic/claude-3.5-sonnet',
            'anthropic/claude-3-haiku',
            'openai/gpt-4o',
            'openai/gpt-4o-mini',
            'google/gemini-pro-1.5'
        ];
    }

    estimateTokens(text) {
        // 简单估算：平均每4个字符=1个token
        return Math.ceil(text.length / 4);
    }

    async validateModel(modelId, temperature = 0.7) {
        try {
            const testMessages = [
                { role: 'user', content: 'Hi' }
            ];

            const response = await fetch(`${this.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': 'https://smart-qa-preview.local',
                    'X-Title': 'Smart QA - Model Validation'
                },
                body: JSON.stringify({
                    model: modelId.replace('openrouter/', ''),
                    messages: testMessages,
                    temperature,
                    max_tokens: 10
                })
            });

            if (!response.ok) {
                const error = await response.text();
                return { valid: false, error: `API error: ${response.status} - ${error}` };
            }

            const data = await response.json();
            if (data.choices && data.choices.length > 0) {
                return { valid: true };
            } else {
                return { valid: false, error: 'No valid response received' };
            }
        } catch (error) {
            return { 
                valid: false, 
                error: error instanceof Error ? error.message : 'Validation failed' 
            };
        }
    }
}

export class GoogleAIProvider {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
    }

    async chat(messages, model) {
        try {
            // 转换消息格式为 Google AI 格式
            const contents = this.convertMessagesToGoogleFormat(messages);
            
            const response = await fetch(`${this.baseUrl}/models/${model.replace('google/', '')}:generateContent?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents,
                    generationConfig: {
                        temperature: 0.7,
                        maxOutputTokens: 4096
                    }
                })
            });

            if (!response.ok) {
                const error = await response.text();
                throw new Error(`Google AI API error: ${response.status} - ${error}`);
            }

            const data = await response.json();
            
            return {
                content: data.candidates[0]?.content?.parts[0]?.text || '',
                model,
                usage: data.usageMetadata ? {
                    promptTokens: data.usageMetadata.promptTokenCount,
                    completionTokens: data.usageMetadata.candidatesTokenCount,
                    totalTokens: data.usageMetadata.totalTokenCount
                } : undefined
            };
        } catch (error) {
            return {
                content: '',
                model,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }

    async *streamChat(messages, model) {
        try {
            const contents = this.convertMessagesToGoogleFormat(messages);
            
            const response = await fetch(`${this.baseUrl}/models/${model.replace('google/', '')}:streamGenerateContent?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents,
                    generationConfig: {
                        temperature: 0.7,
                        maxOutputTokens: 4096
                    }
                })
            });

            if (!response.ok) {
                const error = await response.text();
                yield { content: '', done: true, error: `Google AI API error: ${response.status} - ${error}` };
                return;
            }

            const reader = response.body?.getReader();
            if (!reader) {
                yield { content: '', done: true, error: 'No response body' };
                return;
            }

            let buffer = '';
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += new TextDecoder().decode(value);
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.trim()) {
                        try {
                            const data = JSON.parse(line);
                            const content = data.candidates[0]?.content?.parts[0]?.text || '';
                            if (content) {
                                yield { content, done: false };
                            }
                            if (data.candidates[0]?.finishReason) {
                                yield { content: '', done: true };
                                return;
                            }
                        } catch (e) {
                            // 忽略解析错误的行
                        }
                    }
                }
            }

            yield { content: '', done: true };
        } catch (error) {
            yield { content: '', done: true, error: error instanceof Error ? error.message : 'Unknown error occurred' };
        }
    }

    convertMessagesToGoogleFormat(messages) {
        const contents = [];
        let systemPrompt = '';

        for (const message of messages) {
            if (message.role === 'system') {
                systemPrompt += message.content + '\n';
            } else {
                contents.push({
                    role: message.role === 'user' ? 'user' : 'model',
                    parts: [{ text: message.content }]
                });
            }
        }

        // 如果有系统提示，将其合并到最后一条用户消息
        if (systemPrompt) {
            let lastUserIndex = -1;
            for (let i = contents.length - 1; i >= 0; i--) {
                if (contents[i].role === 'user') { 
                    lastUserIndex = i; 
                    break; 
                }
            }
            if (lastUserIndex >= 0) {
                contents[lastUserIndex].parts[0].text = systemPrompt + contents[lastUserIndex].parts[0].text;
            } else {
                contents.unshift({ role: 'user', parts: [{ text: systemPrompt }] });
            }
        }

        return contents;
    }

    getAvailableModels() {
        return [
            'gemini-pro',
            'gemini-1.5-pro'
        ];
    }

    estimateTokens(text) {
        // Google AI 的 token 计算略有不同
        return Math.ceil(text.length / 3.5);
    }

    async validateModel(modelId, temperature = 0.7) {
        try {
            const testMessages = [
                { role: 'user', content: 'Hi' }
            ];

            const contents = this.convertMessagesToGoogleFormat(testMessages);
            
            const response = await fetch(`${this.baseUrl}/models/${modelId.replace('google/', '')}:generateContent?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents,
                    generationConfig: {
                        temperature,
                        maxOutputTokens: 10
                    }
                })
            });

            if (!response.ok) {
                const error = await response.text();
                return { valid: false, error: `Google AI API error: ${response.status} - ${error}` };
            }

            const data = await response.json();
            if (data.candidates && data.candidates.length > 0) {
                return { valid: true };
            } else {
                return { valid: false, error: 'No valid response received from Google AI' };
            }
        } catch (error) {
            return { 
                valid: false, 
                error: error instanceof Error ? error.message : 'Google AI validation failed' 
            };
        }
    }
}

export class AIService {
    constructor(openRouterKey, googleAIKey) {
        this.providers = new Map();
        this.updateKeys(openRouterKey, googleAIKey);
    }

    updateKeys(openRouterKey, googleAIKey) {
        this.providers.clear();
        if (openRouterKey) {
            this.providers.set('openrouter', new OpenRouterProvider(openRouterKey));
        }
        if (googleAIKey) {
            this.providers.set('google', new GoogleAIProvider(googleAIKey));
        }
    }

    async chat(messages, model, stream = false) {
        const provider = this.getProvider(model);
        if (!provider) {
            return {
                content: '',
                model,
                error: `No provider available for model: ${model}`
            };
        }

        return await provider.chat(messages, model, stream);
    }

    async *streamChat(messages, model) {
        const provider = this.getProvider(model);
        if (!provider) {
            yield { content: '', done: true, error: `No provider available for model: ${model}` };
            return;
        }

        yield* provider.streamChat(messages, model);
    }

    estimateTokens(text, model) {
        const provider = this.getProvider(model);
        if (!provider) {
            return Math.ceil(text.length / 4); // 默认估算
        }
        return provider.estimateTokens(text);
    }

    getAvailableModels() {
        const result = [];
        for (const [providerName, provider] of this.providers) {
            result.push({
                provider: providerName,
                models: provider.getAvailableModels()
            });
        }
        return result;
    }

    isModelAvailable(model) {
        return this.getProvider(model) !== null;
    }

    async validateModel(modelId, temperature = 0.7) {
        const provider = this.getProvider(modelId);
        if (!provider) {
            return { valid: false, error: `No provider available for model: ${modelId}` };
        }
        return await provider.validateModel(modelId, temperature);
    }

    getProvider(model) {
        if (model.startsWith('openrouter/')) {
            return this.providers.get('openrouter') || null;
        }
        if (model.startsWith('google/')) {
            return this.providers.get('google') || null;
        }
        return null;
    }
}

// 导出一个全局 AI 服务实例
window.__aiService = null;

export function createAIService(openRouterKey, googleAIKey) {
    const service = new AIService(openRouterKey, googleAIKey);
    window.__aiService = service;
    return service;
}

export function getAIService() {
    return window.__aiService;
}

console.log('[AI Service] Module loaded');