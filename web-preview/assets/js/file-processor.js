// File Processor for Browser Environment
// Ported from src/file-processor.ts

import { getAllTags } from './obsidian-mock.js';
import { getSettings } from './mock-data.js';

export class FileProcessor {
    constructor(app) {
        this.app = app;
    }

    /**
     * 获取当前选择的文件夹中的文件
     */
    getSelectedFolderFiles() {
        const settings = getSettings();
        if (settings.selectedFolder && settings.selectedFolder.files) {
            return settings.selectedFolder.files.map(fileInfo => ({
                name: fileInfo.name,
                path: fileInfo.path,
                size: fileInfo.size,
                parent: { path: fileInfo.path.split('/').slice(0, -1).join('/') },
                file: fileInfo.file // 真实的File对象
            }));
        }
        return [];
    }

    /**
     * 检查是否有选择的文件夹
     */
    hasSelectedFolder() {
        const settings = getSettings();
        return !!(settings.selectedFolder && settings.selectedFolder.files);
    }

    /**
     * 根据选择模式获取文件
     */
    async getFilesBySelection(mode, selection) {
        // 优先使用选择的文件夹中的文件
        if (this.hasSelectedFolder()) {
            const selectedFiles = this.getSelectedFolderFiles();
            // 只处理markdown文件
            const markdownFiles = selectedFiles.filter(file => 
                file.name.endsWith('.md') || file.name.endsWith('.markdown')
            );

            switch (mode) {
                case 'all':
                    return markdownFiles;
                
                case 'folder':
                    if (!selection || typeof selection !== 'string') {
                        console.log(`[FileProcessor] Invalid folder selection:`, selection);
                        return [];
                    }
                    // 确保文件夹路径以 '/' 结尾进行正确匹配
                    const folderPath = selection.endsWith('/') ? selection : selection + '/';
                    console.log(`[FileProcessor] Looking for files in folder: "${selection}"`);
                    console.log(`[FileProcessor] Folder path with slash: "${folderPath}"`);
                    console.log(`[FileProcessor] Available files:`, markdownFiles.map(f => f.path));

                    const matchedFiles = markdownFiles.filter(file => {
                        const pathMatch = file.path.startsWith(folderPath);
                        const parentMatch = file.parent?.path === selection;
                        const matches = pathMatch || parentMatch;

                        if (matches) {
                            console.log(`[FileProcessor] ✓ Matched file: ${file.path} (parent: ${file.parent?.path})`);
                        }
                        return matches;
                    });

                    console.log(`[FileProcessor] Found ${matchedFiles.length} files in folder "${selection}"`);
                    return matchedFiles;
                
                case 'tags':
                    if (!selection || !Array.isArray(selection)) return [];
                    const filesWithTags = [];
                    
                    for (const file of markdownFiles) {
                        // 对于真实文件，我们需要读取内容来获取标签
                        try {
                            const content = await this.readRealFile(file.file);
                            const fileTags = this.extractTagsFromContent(content);
                            if (selection.some(tag => fileTags.includes(tag))) {
                                filesWithTags.push(file);
                            }
                        } catch (error) {
                            console.warn(`Failed to read file ${file.path}:`, error);
                        }
                    }
                    return filesWithTags;
                
                default:
                    return [];
            }
        }

        // fallback到模拟数据
        console.log(`[FileProcessor] Fallback to mock data`);
        const allFiles = this.app.vault.getMarkdownFiles();
        console.log(`[FileProcessor] Mock files available:`, allFiles.length);

        switch (mode) {
            case 'all':
                return allFiles;

            case 'folder':
                if (!selection || typeof selection !== 'string') {
                    console.log(`[FileProcessor] Invalid folder selection in fallback:`, selection);
                    return [];
                }
                // 确保文件夹路径以 '/' 结尾进行正确匹配
                const folderPath2 = selection.endsWith('/') ? selection : selection + '/';
                console.log(`[FileProcessor] Fallback: Looking for files in folder: "${selection}"`);
                console.log(`[FileProcessor] Fallback: Available mock files:`, allFiles.map(f => f.path));

                const fallbackMatched = allFiles.filter(file => {
                    const pathMatch = file.path.startsWith(folderPath2);
                    const parentMatch = file.parent?.path === selection;
                    const matches = pathMatch || parentMatch;

                    if (matches) {
                        console.log(`[FileProcessor] Fallback ✓ Matched file: ${file.path} (parent: ${file.parent?.path})`);
                    }
                    return matches;
                });

                console.log(`[FileProcessor] Fallback: Found ${fallbackMatched.length} files in folder "${selection}"`);
                return fallbackMatched;
            
            case 'tags':
                if (!selection || !Array.isArray(selection)) return [];
                const filesWithTags = [];
                
                for (const file of allFiles) {
                    const metadata = this.app.metadataCache.getFileCache(file);
                    if (metadata) {
                        const fileTags = getAllTags(metadata) || [];
                        if (selection.some(tag => fileTags.includes(tag))) {
                            filesWithTags.push(file);
                        }
                    }
                }
                return filesWithTags;
            
            default:
                return [];
        }
    }

    /**
     * 读取真实文件内容
     */
    async readRealFile(file) {
        console.log(`[FileProcessor] Reading real file: ${file.name}, size: ${file.size}`);
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const content = e.target.result;
                console.log(`[FileProcessor] File read successfully: ${file.name}, content length: ${content?.length || 0}`);
                if (content && content.length > 0) {
                    console.log(`[FileProcessor] Content preview: ${content.substring(0, 100)}...`);
                }
                resolve(content);
            };
            reader.onerror = (e) => {
                console.error(`[FileProcessor] Failed to read file: ${file.name}`, e);
                reject(e);
            };
            reader.readAsText(file, 'UTF-8');
        });
    }

    /**
     * 从内容中提取标签
     */
    extractTagsFromContent(content) {
        const tags = [];
        
        // 提取 #tag 格式的标签
        const hashTagMatches = content.match(/#[\w-]+/g);
        if (hashTagMatches) {
            tags.push(...hashTagMatches);
        }
        
        // 提取YAML front matter中的标签
        const frontMatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
        if (frontMatterMatch) {
            const frontMatter = frontMatterMatch[1];
            const tagsMatch = frontMatter.match(/tags?:\s*\[(.*?)\]/);
            if (tagsMatch) {
                const yamlTags = tagsMatch[1].split(',').map(tag => 
                    tag.trim().replace(/['"]/g, '').startsWith('#') ? 
                    tag.trim().replace(/['"]/g, '') : 
                    '#' + tag.trim().replace(/['"]/g, '')
                );
                tags.push(...yamlTags);
            }
        }
        
        return [...new Set(tags)]; // 去重
    }

    /**
     * 处理单个文件
     */
    async processFile(file) {
        try {
            console.log(`[FileProcessor] Processing file:`, file);
            let content;
            let tags = [];

            // 检查是否是真实文件对象
            if (file.file && file.file instanceof File) {
                console.log(`[FileProcessor] Processing real file: ${file.path}`);
                // 处理真实文件
                content = await this.readRealFile(file.file);
                console.log(`[FileProcessor] Read content length: ${content?.length || 0}`);
                tags = this.extractTagsFromContent(content);
                console.log(`[FileProcessor] Extracted tags:`, tags);
            } else {
                console.log(`[FileProcessor] Processing mock file: ${file.path}`);
                // 处理模拟文件
                content = await this.app.vault.read(file);
                const metadata = this.app.metadataCache.getFileCache(file);
                tags = getAllTags(metadata) || [];
            }

            if (!content) {
                console.warn(`[FileProcessor] No content for file: ${file.path || file.name}`);
                return null;
            }

            // 预处理内容
            const processedContent = this.preprocessContent(content);
            const wordCount = this.countWords(processedContent);

            console.log(`[FileProcessor] Processed file ${file.path || file.name}: ${wordCount} words`);

            return {
                file,
                content: processedContent,
                metadata: null, // 真实文件暂不提供metadata
                tags,
                wordCount
            };
        } catch (error) {
            console.error(`Failed to process file ${file.path || file.name}:`, error);
            return null;
        }
    }

    /**
     * 批量处理文件
     */
    async processFiles(files, maxFiles = 100) {
        console.log(`[FileProcessor] Processing ${files.length} files (max: ${maxFiles})`);

        // 限制文件数量，避免超出上下文限制
        const filesToProcess = files.slice(0, maxFiles);
        const fileInfos = [];
        let totalWordCount = 0;
        let processedCount = 0;
        let failedCount = 0;

        for (const file of filesToProcess) {
            console.log(`[FileProcessor] Processing file ${processedCount + 1}/${filesToProcess.length}: ${file.path || file.name}`);
            const fileInfo = await this.processFile(file);
            if (fileInfo) {
                fileInfos.push(fileInfo);
                totalWordCount += fileInfo.wordCount;
                processedCount++;
                console.log(`[FileProcessor] ✅ Successfully processed: ${file.path || file.name} (${fileInfo.wordCount} words)`);
            } else {
                failedCount++;
                console.log(`[FileProcessor] ❌ Failed to process: ${file.path || file.name}`);
            }
        }

        console.log(`[FileProcessor] Processing complete: ${processedCount} successful, ${failedCount} failed, ${totalWordCount} total words`);

        // 生成摘要信息
        const summary = this.generateSummary(fileInfos);

        return {
            files: fileInfos,
            totalWordCount,
            summary
        };
    }

    /**
     * 预处理 Markdown 内容
     */
    preprocessContent(content) {
        if (!content || typeof content !== 'string') {
            console.warn(`[FileProcessor] Invalid content for preprocessing:`, typeof content);
            return '';
        }

        const originalLength = content.length;
        console.log(`[FileProcessor] Preprocessing content, original length: ${originalLength}`);

        // 移除过多的空行
        content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

        // 处理 Obsidian 特有的语法
        // 移除 YAML front matter（但保留有用信息）
        content = content.replace(/^---[\s\S]*?---\n/m, '');

        // 转换内部链接格式
        content = content.replace(/\[\[([^\]]+)\]\]/g, '$1');

        // 转换标签格式
        content = content.replace(/#([\w-]+)/g, 'tag:$1');

        // 清理多余的 Markdown 格式（保留重要的）
        // 保留标题、列表、粗体、斜体，但移除过于复杂的格式

        // 移除 HTML 标签
        content = content.replace(/<[^>]*>/g, '');

        // 移除过多的标点符号重复
        content = content.replace(/[!?.]{3,}/g, '...');

        // 统一换行符
        content = content.replace(/\r\n/g, '\n');

        const processed = content.trim();
        console.log(`[FileProcessor] Preprocessing complete, final length: ${processed.length}`);

        return processed;
    }

    /**
     * 统计词数
     */
    countWords(content) {
        if (!content || typeof content !== 'string') {
            console.warn(`[FileProcessor] Invalid content for word count:`, typeof content, content?.length);
            return 0;
        }

        // 简单的词数统计
        const words = content.split(/\s+/).filter(word => word.length > 0);
        console.log(`[FileProcessor] Word count: ${words.length} from content length: ${content.length}`);
        return words.length;
    }

    /**
     * 生成内容摘要
     */
    generateSummary(fileInfos, originalStats) {
        // 如果提供了原始统计信息，使用原始统计；否则使用当前文件信息计算
        if (originalStats) {
            return `Files: ${originalStats.fileCount} | Words: ${originalStats.totalWords.toLocaleString()} | Folders: ${originalStats.folders} | Tags: ${originalStats.tags}`;
        }

        const fileCount = fileInfos.length;
        const totalWords = fileInfos.reduce((sum, info) => sum + info.wordCount, 0);

        // 统计文件类型
        const folders = new Set(fileInfos.map(info => info.file.parent?.path || 'root'));
        const tags = new Set(fileInfos.flatMap(info => info.tags));

        // 保留核心统计信息
        return `Files: ${fileCount} | Words: ${totalWords.toLocaleString()} | Folders: ${folders.size} | Tags: ${tags.size}`;
    }

    /**
     * 将处理后的内容转换为 AI 可理解的格式
     */
    formatForAI(processedContent, includeFileNames = true) {
        let formattedContent = '';
        
        if (includeFileNames) {
            formattedContent += `=== KNOWLEDGE BASE CONTENT (${processedContent.files.length} files) ===\n\n`;
        }

        for (const fileInfo of processedContent.files) {
            if (includeFileNames) {
                formattedContent += `## File: ${fileInfo.file.name}\n`;
                formattedContent += `Path: ${fileInfo.file.path}\n`;
                if (fileInfo.tags.length > 0) {
                    formattedContent += `Tags: ${fileInfo.tags.join(', ')}\n`;
                }
                formattedContent += `\n`;
            }
            
            formattedContent += fileInfo.content;
            formattedContent += '\n\n---\n\n';
        }

        return formattedContent;
    }

    /**
     * 获取所有可用的文件夹
     */
    getAllFolders() {
        // 如果有选择的文件夹，从真实文件中获取
        if (this.hasSelectedFolder()) {
            const folders = new Set();
            const selectedFiles = this.getSelectedFolderFiles();
            
            for (const file of selectedFiles) {
                const folderPath = file.path.split('/').slice(0, -1).join('/');
                if (folderPath) {
                    folders.add(folderPath);
                }
            }
            
            return Array.from(folders).sort();
        }
        
        // fallback到模拟数据
        const folders = new Set();
        const files = this.app.vault.getMarkdownFiles();
        
        for (const file of files) {
            if (file.parent && file.parent.path !== '/') {
                folders.add(file.parent.path);
            }
        }
        
        return Array.from(folders).sort();
    }

    /**
     * 获取所有可用的标签
     */
    getAllTags() {
        const tags = new Set();
        const files = this.app.vault.getMarkdownFiles();
        
        for (const file of files) {
            const metadata = this.app.metadataCache.getFileCache(file);
            if (metadata) {
                const fileTags = getAllTags(metadata) || [];
                fileTags.forEach(tag => tags.add(tag));
            }
        }
        
        return Array.from(tags).sort();
    }

    /**
     * 获取文件的完整统计信息（不处理内容）
     */
    async getFileStats(files) {
        const folders = new Set();
        const tags = new Set();
        let totalWords = 0;

        for (const file of files) {
            // 统计文件夹
            folders.add(file.parent?.path || 'root');

            // 统计标签（web-preview中的模拟实现）
            if (file.tags) {
                file.tags.forEach(tag => tags.add(tag));
            }

            // 统计字数
            try {
                let content = '';
                if (file.file && file.file instanceof File) {
                    content = await file.file.text();
                } else if (file.content) {
                    content = file.content;
                }

                if (content) {
                    const processedContent = this.preprocessContent(content);
                    totalWords += this.countWords(processedContent);
                }
            } catch (error) {
                console.warn(`Failed to read file ${file.path || file.name} for stats:`, error);
            }
        }

        return {
            fileCount: files.length,
            totalWords,
            folders: folders.size,
            tags: tags.size
        };
    }

    /**
     * 统计所有标签及其出现次数
     */
    async getTagCounts() {
        const tagToCount = new Map();
        
        // 如果有选择的文件夹，从真实文件中获取
        if (this.hasSelectedFolder()) {
            const selectedFiles = this.getSelectedFolderFiles();
            const markdownFiles = selectedFiles.filter(file => 
                file.name.endsWith('.md') || file.name.endsWith('.markdown')
            );
            
            for (const file of markdownFiles) {
                try {
                    const content = await this.readRealFile(file.file);
                    const fileTags = this.extractTagsFromContent(content);
                    for (const tag of fileTags) {
                        tagToCount.set(tag, (tagToCount.get(tag) || 0) + 1);
                    }
                } catch (error) {
                    console.warn(`Failed to read file ${file.path}:`, error);
                }
            }
        } else {
            // fallback到模拟数据
            const files = this.app.vault.getMarkdownFiles();
            
            for (const file of files) {
                const metadata = this.app.metadataCache.getFileCache(file);
                if (!metadata) continue;
                
                const fileTags = getAllTags(metadata) || [];
                for (const tag of fileTags) {
                    tagToCount.set(tag, (tagToCount.get(tag) || 0) + 1);
                }
            }
        }
        
        return Array.from(tagToCount.entries())
            .map(([tag, count]) => ({ tag, count }))
            .sort((a, b) => b.count - a.count || a.tag.localeCompare(b.tag));
    }

    /**
     * 估算内容的 token 数量（简单估算）
     */
    estimateTokens(content) {
        // 更精确的 token 估算
        // 英文平均 4 字符 = 1 token
        // 中文平均 1.5 字符 = 1 token
        const englishChars = (content.match(/[a-zA-Z0-9\s]/g) || []).length;
        const chineseChars = (content.match(/[\u4e00-\u9fff]/g) || []).length;
        const otherChars = content.length - englishChars - chineseChars;
        
        const englishTokens = Math.ceil(englishChars / 4);
        const chineseTokens = Math.ceil(chineseChars / 1.5);
        const otherTokens = Math.ceil(otherChars / 3);
        
        return englishTokens + chineseTokens + otherTokens;
    }

    /**
     * 智能截断内容以适应 token 限制
     */
    truncateContent(processedContent, maxTokens, originalStats) {
        const { files } = processedContent;
        const truncatedFiles = [];
        let currentTokens = 0;
        
        // 按文件大小排序，优先保留小文件
        const sortedFiles = [...files].sort((a, b) => a.wordCount - b.wordCount);
        
        for (const fileInfo of sortedFiles) {
            const fileTokens = this.estimateTokens(fileInfo.content);
            
            if (currentTokens + fileTokens <= maxTokens) {
                truncatedFiles.push(fileInfo);
                currentTokens += fileTokens;
            } else {
                // 如果单个文件就超过限制，截断该文件
                const remainingTokens = maxTokens - currentTokens;
                if (remainingTokens > 100) { // 至少保留100个token的空间
                    const truncatedContent = this.truncateText(fileInfo.content, remainingTokens);
                    truncatedFiles.push({
                        ...fileInfo,
                        content: truncatedContent + '\n\n[Content truncated...]',
                        wordCount: this.countWords(truncatedContent)
                    });
                }
                break;
            }
        }
        
        return {
            files: truncatedFiles,
            totalWordCount: truncatedFiles.reduce((sum, info) => sum + info.wordCount, 0),
            summary: this.generateSummary(truncatedFiles, originalStats)
        };
    }

    /**
     * 截断单个文本到指定 token 数
     */
    truncateText(text, maxTokens) {
        const estimatedCharsPerToken = 4;
        const maxChars = maxTokens * estimatedCharsPerToken;
        
        if (text.length <= maxChars) {
            return text;
        }
        
        // 在句子边界截断
        const truncated = text.substring(0, maxChars);
        const lastSentenceEnd = Math.max(
            truncated.lastIndexOf('.'),
            truncated.lastIndexOf('!'),
            truncated.lastIndexOf('?'),
            truncated.lastIndexOf('\n\n')
        );
        
        if (lastSentenceEnd > maxChars * 0.8) {
            return truncated.substring(0, lastSentenceEnd + 1);
        }
        
        return truncated;
    }
}

console.log('[File Processor] Module loaded');