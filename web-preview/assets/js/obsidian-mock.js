// Enhanced Obsidian API mock for browser environment
// Based on the existing preview/obsidian-mock.ts

export class TFile {
    constructor(path) {
        this.path = path;
        const parts = path.split('/');
        this.name = parts[parts.length - 1];
        const parentPath = parts.slice(0, -1).join('/') || '/';
        this.parent = parentPath === '/' ? null : { path: parentPath };
    }
}

export class CachedMetadata {
    constructor(tags = []) {
        this.tags = tags;
    }
}

export function getAllTags(metadata) {
    return metadata?.tags || [];
}

function extractTagsFromContent(content) {
    const tagSet = new Set();
    const tagRegex = /(^|\s)#([\w-]+)/g;
    let match;
    while ((match = tagRegex.exec(content))) {
        tagSet.add(`#${match[2]}`);
    }
    return Array.from(tagSet);
}

class Vault {
    constructor() {
        this.pathToContent = new Map();
        this.pathToMetadata = new Map();
    }

    setFiles(files) {
        this.pathToContent.clear();
        this.pathToMetadata.clear();
        
        for (const file of files) {
            this.pathToContent.set(file.path, file.content);
            
            // 提取标签创建元数据
            const tags = extractTagsFromContent(file.content);
            this.pathToMetadata.set(file.path, new CachedMetadata(tags));
        }
    }

    getMarkdownFiles() {
        return Array.from(this.pathToContent.keys())
            .filter(path => path.toLowerCase().endsWith('.md'))
            .map(path => new TFile(path));
    }

    async read(file) {
        return this.pathToContent.get(file.path) || '';
    }

    async create(path, content) {
        this.pathToContent.set(path, content);
        const tags = extractTagsFromContent(content);
        this.pathToMetadata.set(path, new CachedMetadata(tags));
        return new TFile(path);
    }

    async createFolder(folderPath) {
        // No-op for preview
        console.log(`[Mock] Created folder: ${folderPath}`);
    }

    getAbstractFileByPath(path) {
        return this.pathToContent.has(path) ? new TFile(path) : null;
    }
}

class MetadataCache {
    constructor(vault) {
        this.vault = vault;
    }

    getFileCache(file) {
        const metadata = this.vault.pathToMetadata.get(file.path);
        if (metadata) {
            return metadata;
        }
        
        // Fallback: derive tags from file content
        const content = this.vault.pathToContent.get(file.path);
        if (content) {
            const tags = extractTagsFromContent(content);
            return new CachedMetadata(tags);
        }
        
        return new CachedMetadata([]);
    }
}

class WorkspaceLeaf {
    constructor(containerEl) {
        this.containerEl = containerEl;
    }

    openFile(file) {
        console.log(`[Mock] Opening file: ${file.path}`);
        // 在新窗口中显示文件内容
        const content = window.__obsidianMockApp.vault.pathToContent.get(file.path) || 'File not found';
        const blob = new Blob([`# ${file.name}\n\n${content}`], { type: 'text/markdown' });
        const url = URL.createObjectURL(blob);
        window.open(url, '_blank');
    }
}

class Workspace {
    constructor() {
        this.activeFile = null;
    }

    getLeaf() {
        const containerEl = document.getElementById('smart-qa-root');
        return new WorkspaceLeaf(containerEl);
    }

    getActiveFile() {
        return this.activeFile;
    }

    setActiveFile(file) {
        this.activeFile = file;
    }

    onLayoutReady(callback) {
        setTimeout(callback, 0);
    }
}

class Settings {
    constructor() {
        this.tabs = new Map();
    }

    open() {
        console.log('[Mock] Opening settings');
        // 显示配置面板
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.scrollIntoView({ behavior: 'smooth' });
        }
    }

    openTabById(id) {
        console.log(`[Mock] Opening settings tab: ${id}`);
    }

    addTab(id, tab) {
        this.tabs.set(id, tab);
    }
}

export class App {
    constructor() {
        this.vault = new Vault();
        this.metadataCache = new MetadataCache(this.vault);
        this.workspace = new Workspace();
        this.setting = new Settings();
    }
}

export class Plugin {
    constructor(app) {
        this.app = app || window.__obsidianMockApp;
        this.id = 'smart-qa';
        this.settings = {};
    }

    async loadData() {
        const raw = localStorage.getItem('smart-qa-data');
        return raw ? JSON.parse(raw) : null;
    }

    async saveData(data) {
        localStorage.setItem('smart-qa-data', JSON.stringify(data));
    }

    onload() {
        console.log('[Mock] Plugin loaded');
    }

    onunload() {
        console.log('[Mock] Plugin unloaded');
    }
}

export class Notice {
    constructor(message, timeout = 2500) {
        this.show(message, timeout);
    }

    static ensureContainer() {
        let container = document.getElementById('mock-notice-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'mock-notice-container';
            container.style.cssText = `
                position: fixed;
                right: 16px;
                bottom: 16px;
                display: flex;
                flex-direction: column-reverse;
                gap: 8px;
                z-index: 10000;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
        return container;
    }

    show(message, timeout) {
        const container = Notice.ensureContainer();
        const item = document.createElement('div');
        
        item.textContent = message;
        item.style.cssText = `
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            max-width: 320px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            pointer-events: auto;
            word-wrap: break-word;
        `;
        
        container.appendChild(item);
        
        // 添加动画效果
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'all 0.3s ease';
        
        requestAnimationFrame(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        });
        
        setTimeout(() => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(-20px)';
            setTimeout(() => item.remove(), 300);
        }, timeout);
    }
}

// Markdown renderer mock
export class MarkdownRenderer {
    static async renderMarkdown(markdown, element, sourcePath = '', component = null) {
        return this.render(null, markdown, element, sourcePath, component);
    }

    static async render(app, markdown, element, sourcePath = '', component = null) {
        // 简单的 Markdown 渲染
        let html = markdown
            // 标题
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            // 粗体和斜体
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            // 代码块
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            // 链接
            .replace(/\[([^\]]+)\]\(([^\)]+)\)/g, '<a href="$2">$1</a>')
            // 列表
            .replace(/^\- (.*$)/gim, '<li>$1</li>')
            .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
            // 段落
            .replace(/\n\n/g, '</p><p>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>')
            // 清理空段落
            .replace(/<p><\/p>/g, '');

        element.innerHTML = html;
    }
}

// 图标系统
export function setIcon(element, iconName) {
    // 简单的 SVG 图标映射
    const icons = {
        'message-circle': '<svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>',
        'save': '<svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path><polyline points="17,21 17,13 7,13 7,21"></polyline><polyline points="7,3 7,8 15,8"></polyline></svg>',
        'plus': '<svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>',
        'settings': '<svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24"></path></svg>',
        'activity': '<svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"><polyline points="22,12 18,12 15,21 9,3 6,12 2,12"></polyline></svg>',
        'bug': '<svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"><rect width="8" height="14" x="8" y="6" rx="4"></rect><path d="M9 9h6M12 1v3M8 4.5L6 6M16 4.5L18 6M12 18v3M8 19.5L6 18M16 19.5L18 18"></path></svg>',
    };

    const iconSvg = icons[iconName] || icons['settings'];
    element.innerHTML = iconSvg;
}

// DOM helper extensions
function installDomHelpers() {
    const proto = HTMLElement.prototype;
    
    if (!proto.createEl) {
        proto.createEl = function(tag, options = {}) {
            const el = document.createElement(tag);
            
            if (options.cls) {
                el.className = options.cls;
            }
            
            if (options.text) {
                el.textContent = options.text;
            }
            
            if (options.type && el.tagName === 'INPUT') {
                el.type = options.type;
            }
            
            if (options.attr) {
                for (const [key, value] of Object.entries(options.attr)) {
                    el.setAttribute(key, String(value));
                }
            }
            
            this.appendChild(el);
            return el;
        };
    }
    
    if (!proto.createSpan) {
        proto.createSpan = function(options = {}) {
            return this.createEl('span', options);
        };
    }
    
    if (!proto.addClass) {
        proto.addClass = function(className) {
            if (className) {
                className.split(/\s+/).filter(Boolean).forEach(cls => {
                    this.classList.add(cls);
                });
            }
        };
    }
    
    if (!proto.removeClass) {
        proto.removeClass = function(className) {
            if (className) {
                className.split(/\s+/).filter(Boolean).forEach(cls => {
                    this.classList.remove(cls);
                });
            }
        };
    }
    
    if (!proto.empty) {
        proto.empty = function() {
            while (this.firstChild) {
                this.removeChild(this.firstChild);
            }
        };
    }
    
    if (!proto.setText) {
        proto.setText = function(text) {
            this.textContent = text;
        };
    }
    
    if (!proto.toggleAttribute) {
        proto.toggleAttribute = function(name, force) {
            if (force === undefined) {
                force = !this.hasAttribute(name);
            }
            if (force) {
                this.setAttribute(name, '');
            } else {
                this.removeAttribute(name);
            }
        };
    }
}

// 初始化 DOM helpers
installDomHelpers();

// 创建全局 mock app 实例
const mockApp = new App();
window.__obsidianMockApp = mockApp;

console.log('[Mock] Obsidian API mock initialized');

export default mockApp;