// Smart QA UI Controller for Browser Environment
// Ported from src/view.ts

import { Notice, MarkdownRenderer, setIcon } from './obsidian-mock.js';
import { AIService, createAIService } from './api-service.js';
import { FileProcessor } from './file-processor.js';
import { getSettings, saveSettings } from './mock-data.js';

export class SmartQAView {
    constructor(containerEl, app) {
        this.app = app;
        this.containerEl = containerEl;
        this.buildMarker = 'build-' + new Date().toISOString() + '-' + Math.random().toString(36).slice(2, 7);
        
        // 核心组件
        this.fileProcessor = new FileProcessor(this.app);
        this.conversationHistory = [];
        this.currentConversationId = null;
        this.isProcessing = false;
        
        // UI 状态
        this.currentSelection = { mode: 'all' };
        this.processedContent = null;
        this.currentModelId = '';
        this.availableTags = [];
        
        // DOM 元素引用
        this.messagesContainer = null;
        this.inputContainer = null;
        this.fileSelectionContainer = null;
        this.statusContainer = null;
        this.collapsedChipEl = null;
        this.selectedChipsEl = null;
        this.tagInputEl = null;
        this.selectionContentEl = null;
        this.selectionHeaderEl = null;
        this.selectionCollapseBtn = null;
        this.modalOverlay = null;
        this.modalContent = null;
        this.isSelectionCollapsed = false;
        
        // 加载设置并初始化
        this.settings = getSettings();
        this.applyFontSize();
        this.initializeAIService();
        this.startNewConversation();
    }

    initializeAIService() {
        this.aiService = createAIService(
            this.settings.openRouterApiKey,
            this.settings.googleAiApiKey
        );
    }

    startNewConversation() {
        this.currentConversationId = 'conv_' + Date.now();
        this.conversationHistory = [];
        if (!this.currentModelId && this.settings.defaultModel) {
            this.currentModelId = this.settings.defaultModel;
        }
    }

    async renderMarkdownTo(element, markdown) {
        await MarkdownRenderer.render(this.app, markdown, element, '', this);
    }

    async render() {
        this.containerEl.empty();
        this.containerEl.addClass('smart-qa-container');
        this.applyFontSize();

        try {
            this.createHeader(this.containerEl);
            await this.createFileSelection(this.containerEl);
            this.createMessages(this.containerEl);
            this.createInputArea(this.containerEl);
            this.createStatusBar(this.containerEl);
        } catch (error) {
            console.error('Failed to render Smart QA view:', error);
        }
    }

    applyFontSize() {
        const base = this.settings?.uiFontSize ?? 14;
        const clamped = Math.min(Math.max(base, 10), 28);
        if (this.containerEl?.style) {
            this.containerEl.style.setProperty('--smart-qa-font-size', `${clamped}px`);
        }
    }

    createHeader(container) {
        const header = container.createEl('div', { cls: 'smart-qa-header' });
        
        // 左侧：折叠胶囊入口
        const leftGroup = header.createEl('div', { cls: 'smart-qa-header-left' });
        const collapsedChip = leftGroup.createEl('button', { cls: 'smart-qa-collapsed-chip' });
        collapsedChip.addEventListener('click', () => {
            this.showFileSelectionModal();
        });
        this.collapsedChipEl = collapsedChip;

        const buttonGroup = header.createEl('div', { cls: 'smart-qa-header-buttons' });
        
        // 保存笔记按钮
        const saveNoteBtn = buttonGroup.createEl('button', { 
            cls: 'smart-qa-btn smart-qa-btn-icon',
            title: 'Save conversation as note'
        });
        setIcon(saveNoteBtn, 'save');
        saveNoteBtn.addEventListener('click', () => this.saveConversationAsNote());

        // 新对话按钮
        const newChatBtn = buttonGroup.createEl('button', { 
            cls: 'smart-qa-btn smart-qa-btn-icon',
            title: 'New conversation'
        });
        setIcon(newChatBtn, 'plus');
        newChatBtn.addEventListener('click', () => this.newConversation());

        // 设置按钮
        const settingsBtn = buttonGroup.createEl('button', { 
            cls: 'smart-qa-btn smart-qa-btn-icon',
            title: 'Settings'
        });
        setIcon(settingsBtn, 'settings');
        settingsBtn.addEventListener('click', () => this.openSettings());
        
        // 测试 API 按钮
        const testApiBtn = buttonGroup.createEl('button', {
            cls: 'smart-qa-btn smart-qa-btn-icon',
            title: 'Test API'
        });
        setIcon(testApiBtn, 'activity');
        testApiBtn.addEventListener('click', () => this.testApiConnectivity());
        
        // UI 调试按钮
        const debugUiBtn = buttonGroup.createEl('button', {
            cls: 'smart-qa-btn smart-qa-btn-icon',
            title: 'Debug UI'
        });
        setIcon(debugUiBtn, 'bug');
        debugUiBtn.addEventListener('click', () => this.runUIHealthCheck());
        
        // 初始化折叠胶囊文本
        this.updateCollapsedChipLabel();
    }

    async createFileSelection(container) {
        this.fileSelectionContainer = container.createEl('div', { cls: 'smart-qa-file-selection' });
        
        const selectionHeader = this.fileSelectionContainer.createEl('div', { cls: 'smart-qa-section-header' });
        selectionHeader.createEl('span', { text: 'File Selection', cls: 'smart-qa-section-title' });
        const collapseBtn = selectionHeader.createEl('button', {
            cls: 'smart-qa-section-collapse smart-qa-btn-icon',
            attr: { 'aria-label': 'Collapse file selection' }
        });
        setIcon(collapseBtn, 'chevron-up');
        collapseBtn.addEventListener('click', (event) => {
            event.preventDefault();
            event.stopPropagation();
            this.toggleSelectionCollapse();
        });
        this.selectionCollapseBtn = collapseBtn;

        const selectionContent = this.fileSelectionContainer.createEl('div', { cls: 'smart-qa-file-selection-content' });
        this.selectionContentEl = selectionContent;
        this.selectionHeaderEl = selectionHeader;
        this.setSelectionCollapse(true); // 默认收起状态
        
        // 选择模式按钮组
        const modeButtons = selectionContent.createEl('div', { cls: 'smart-qa-mode-buttons' });
        
        const allFilesBtn = modeButtons.createEl('button', { 
            cls: 'smart-qa-mode-btn smart-qa-mode-btn-active',
            text: 'All Files'
        });
        
        const folderBtn = modeButtons.createEl('button', { 
            cls: 'smart-qa-mode-btn',
            text: 'Folder'
        });
        
        const tagBtn = modeButtons.createEl('button', { 
            cls: 'smart-qa-mode-btn',
            text: 'Tags'
        });

        // 文件选择区域（初始隐藏）
        const fileOptions = selectionContent.createEl('div', { cls: 'smart-qa-file-options' });
        fileOptions.style.display = 'none';
        
        // 文件夹选择器
        const folderSelector = fileOptions.createEl('div', { cls: 'smart-qa-folder-selector' });
        folderSelector.style.display = 'none';
        folderSelector.createEl('label', { text: 'Select Folder:', cls: 'smart-qa-label' });
        const folderSelect = folderSelector.createEl('select', { cls: 'smart-qa-select' });
        
        // 标签选择器
        const tagSelector = fileOptions.createEl('div', { cls: 'smart-qa-tag-selector' });
        tagSelector.style.display = 'none';
        tagSelector.createEl('label', { text: 'Select Tags:', cls: 'smart-qa-label' });

        // 标签输入
        const tagInputWrapper = tagSelector.createEl('div', { cls: 'smart-qa-tag-input-wrapper' });
        const tagInput = tagInputWrapper.createEl('input', { 
            type: 'text', 
            cls: 'smart-qa-tag-input', 
            attr: { 
                list: 'smart-qa-tag-datalist', 
                placeholder: '输入以筛选或按 Enter 添加，例如 #project' 
            } 
        });
        const tagDataList = tagInputWrapper.createEl('datalist', { attr: { id: 'smart-qa-tag-datalist' } });

        // 已选标签 chips
        const selectedChips = tagSelector.createEl('div', { cls: 'smart-qa-selected-chips' });

        // 排序选择器
        const sortRow = tagSelector.createEl('div', { cls: 'smart-qa-tag-sort-row' });
        sortRow.style.margin = '6px 0';
        sortRow.createEl('span', { text: 'Sort:', cls: 'smart-qa-label' });
        const sortSelect = sortRow.createEl('select', { cls: 'smart-qa-select' });
        sortSelect.createEl('option', { value: 'count-desc', text: 'Count (desc)' });
        sortSelect.createEl('option', { value: 'count-asc', text: 'Count (asc)' });
        sortSelect.createEl('option', { value: 'alpha-asc', text: 'A → Z' });
        sortSelect.createEl('option', { value: 'alpha-desc', text: 'Z → A' });

        // 标签复选列表
        const tagContainer = tagSelector.createEl('div', { cls: 'smart-qa-tag-container' });

        // 状态显示
        const selectionStatus = selectionContent.createEl('div', { cls: 'smart-qa-selection-status' });
        selectionStatus.setText('All files in vault will be included');

        // 初始化选择器内容
        await this.populateSelectors(folderSelect, tagContainer, tagDataList, selectedChips, tagInput, sortSelect);
        
        [allFilesBtn, folderBtn, tagBtn].forEach(btn => {
            btn.addEventListener('click', () => {
                // 移除所有按钮的active类
                modeButtons.querySelectorAll('.smart-qa-mode-btn').forEach(b => {
                    b.classList.remove('smart-qa-mode-btn-active');
                });
                // 给当前按钮添加active类
                btn.classList.add('smart-qa-mode-btn-active');

                // 隐藏所有选择器
                folderSelector.style.display = 'none';
                tagSelector.style.display = 'none';
                fileOptions.style.display = 'none';

                // 显示相应的选择器
                if (btn.textContent === 'Folder') {
                    fileOptions.style.display = 'block';
                    folderSelector.style.display = 'block';
                } else if (btn.textContent === 'Tags') {
                    fileOptions.style.display = 'block';
                    tagSelector.style.display = 'block';
                }

                this.updateFileSelection(btn.textContent || '');
            });
        });
        
        // 文件夹选择事件
        folderSelect.addEventListener('change', () => {
            const selectedFolder = folderSelect.value;
            console.log(`[UIController] Folder selected: "${selectedFolder}"`);
            this.showDebugInfo(`用户选择了文件夹: "${selectedFolder}"`);

            this.currentSelection = { mode: 'folder', selection: selectedFolder };
            this.processedContent = null;
            this.loadSelectedFiles().then(() => {
                this.updateFileSelectionStatus();
            });

            // 自动高亮"Folder"模式按钮
            modeButtons.querySelectorAll('.smart-qa-mode-btn').forEach(b => {
                b.classList.remove('smart-qa-mode-btn-active');
            });
            folderBtn.classList.add('smart-qa-mode-btn-active');
        });
        
        // 排序选择事件
        sortSelect.addEventListener('change', async () => {
            await this.populateSelectors(folderSelect, tagContainer, tagDataList, selectedChips, tagInput, sortSelect);
        });
    }

    createMessages(container) {
        this.messagesContainer = container.createEl('div', { cls: 'smart-qa-messages' });
    }

    createStatusBar(container) {
        this.statusContainer = container.createEl('div', { cls: 'smart-qa-status' });
        this.updateStatus('Ready', this.getStatusText());
    }

    createInputArea(container) {
        this.inputContainer = container.createEl('div', { cls: 'smart-qa-input-container' });
        
        const inputWrapper = this.inputContainer.createEl('div', { cls: 'smart-qa-input-wrapper' });
        
        const textarea = inputWrapper.createEl('textarea', { 
            cls: 'smart-qa-input',
            placeholder: 'Ask a question about your knowledge base...'
        });
        textarea.rows = 1;

        // 底部内嵌工具条
        const toolbar = inputWrapper.createEl('div', { cls: 'smart-qa-input-toolbar' });
        const toolbarLeft = toolbar.createEl('div', { cls: 'smart-qa-input-toolbar-left' });
        const toolbarRight = toolbar.createEl('div', { cls: 'smart-qa-input-toolbar-right' });
        
        // 模型选择器
        const modelSelector = toolbarLeft.createEl('select', { 
            cls: 'smart-qa-model-selector',
            title: 'Select AI Model'
        });
        this.populateModelSelector(modelSelector);
        
        // Token 计数显示
        const tokenCount = toolbarRight.createEl('span', { 
            cls: 'smart-qa-token-count',
            text: '0'
        });

        // 发送按钮
        const sendBtn = toolbarRight.createEl('button', { 
            cls: 'smart-qa-btn smart-qa-send-btn',
            title: 'Send (Enter) · Shift+Enter for newline',
            attr: { 'aria-label': 'Send' }
        });
        sendBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m22 2-7 20-4-9-9-4z"/><path d="m22 2-10 10"/></svg>`;
        
        sendBtn.style.visibility = 'visible';
        sendBtn.style.display = 'inline-flex';

        // 自动调整文本框高度 + UI 状态切换
        const updateTypingState = () => {
            const hasText = textarea.value.trim().length > 0;
            if (hasText) {
                inputWrapper.addClass('has-text');
            } else {
                inputWrapper.removeClass('has-text');
            }
            sendBtn.toggleAttribute('disabled', !hasText);
        };

        textarea.addEventListener('input', () => {
            // 自适应高度
            const cs = getComputedStyle(textarea);
            let lh = parseFloat(cs.lineHeight);
            if (Number.isNaN(lh)) {
                const fs = parseFloat(cs.fontSize);
                lh = !Number.isNaN(fs) ? Math.round(fs * 1.5) : 20;
            }
            const maxH = Math.max(24, Math.round(lh * 3));
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, maxH) + 'px';
            this.updateTokenCount(textarea.value, tokenCount);
            updateTypingState();
        });

        textarea.addEventListener('focus', () => inputWrapper.addClass('is-focused'));
        textarea.addEventListener('blur', () => inputWrapper.removeClass('is-focused'));

        // 发送消息
        let isComposing = false;
        textarea.addEventListener('compositionstart', () => { 
            isComposing = true; 
        });
        textarea.addEventListener('compositionend', () => { 
            isComposing = false; 
        });
        
        const sendMessage = () => {
            const message = textarea.value.trim();
            
            if (message) {
                this.handleUserMessage(message);
                textarea.value = '';
                textarea.style.height = 'auto';
                this.updateTokenCount('', tokenCount);
                updateTypingState();
            }
        };

        // 模型选择器事件
        modelSelector.addEventListener('change', () => {
            this.currentModelId = modelSelector.value;
            this.updateStatus('Ready', this.getStatusText());
        });

        sendBtn.addEventListener('click', sendMessage);
        
        textarea.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !isComposing) {
                if (e.shiftKey) {
                    return; // 允许 Shift+Enter 换行
                }
                e.preventDefault();
                sendMessage();
            }
        });
    }

    populateModelSelector(selector) {
        selector.empty();
        
        if (this.settings.customModels.length === 0) {
            selector.createEl('option', { value: '', text: 'Select a model…' });
            selector.addClass('smart-qa-model-placeholder');
            selector.title = 'Open settings to add models';
            this.currentModelId = '';
            this.updateStatus('Ready', this.getStatusText());
            return;
        }
        
        this.settings.customModels.forEach(model => {
            // 如果名称是技术格式(provider/modelId)，生成友好名称
            let displayName = model.name;
            if (model.name.includes('/')) {
                // 从modelId生成友好名称
                const modelIdParts = model.modelId.split('/').pop() || model.modelId;
                const friendlyName = modelIdParts
                    .replace(/-/g, ' ')
                    .replace(/\./g, ' ')
                    .split(' ')
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(' ');
                displayName = friendlyName;
            }
            
            selector.createEl('option', { 
                text: displayName, 
                attr: { 
                    value: model.id,
                    title: `${displayName} (${model.provider})` 
                }
            });
        });
        selector.removeClass('smart-qa-model-placeholder');
        
        const defaultId = this.settings.defaultModel;
        const hasDefault = !!defaultId && this.settings.customModels.some(m => m.id === defaultId);
        const selectedId = hasDefault ? defaultId : this.settings.customModels[0].id;
        selector.value = selectedId;
        this.currentModelId = selectedId;
        this.updateStatus('Ready', this.getStatusText());
    }

    async populateSelectors(folderSelect, tagContainer, tagDataList, selectedChips, tagInput, sortSelect) {
        // 填充文件夹选择器
        console.log('[UIController] Populating selectors...');

        const folders = this.fileProcessor.getAllFolders();
        console.log('[UIController] Available folders:', folders);

        folderSelect.empty();

        if (!this.fileProcessor.hasSelectedFolder()) {
            // 如果没有选择根文件夹，提示用户先选择
            console.log('[UIController] No folder selected, showing hint');
            folderSelect.createEl('option', {
                value: '',
                text: '👈 请先在左侧选择知识库文件夹'
            });
            this.showDebugInfo('⚠️ 请先在左侧选择知识库文件夹');
        } else if (folders.length === 0) {
            console.log('[UIController] No subfolders found');
            folderSelect.createEl('option', {
                value: '',
                text: '知识库中暂无子文件夹'
            });
            this.showDebugInfo('ℹ️ 知识库中暂无子文件夹，将使用所有文件');
        } else {
            console.log('[UIController] Adding folder options:', folders);
            folderSelect.createEl('option', { value: '', text: 'Select a folder...' });
            folders.forEach(folder => {
                folderSelect.createEl('option', { value: folder, text: folder });
            });
            this.showDebugInfo(`✅ 找到 ${folders.length} 个子文件夹: ${folders.join(', ')}`);
        }

        // 获取标签与计数（异步）
        const tagCounts = await this.fileProcessor.getTagCounts();
        let tags = [];
        const sortMode = sortSelect?.value || 'count-desc';
        
        if (sortMode.startsWith('count')) {
            const sorted = [...tagCounts].sort((a, b) => 
                sortMode === 'count-desc' ? 
                (b.count - a.count || a.tag.localeCompare(b.tag)) : 
                (a.count - b.count || a.tag.localeCompare(b.tag))
            );
            tags = sorted.map(x => x.tag);
        } else if (sortMode === 'alpha-asc') {
            tags = [...tagCounts.map(x => x.tag)].sort((a, b) => a.localeCompare(b));
        } else {
            tags = [...tagCounts.map(x => x.tag)].sort((a, b) => b.localeCompare(a));
        }
        
        this.availableTags = tags;

        // 渲染标签复选
        tagContainer.empty();
        if (tags.length === 0) {
            tagContainer.createEl('div', { text: 'No tags found in your vault', cls: 'smart-qa-no-tags' });
        } else {
            tags.forEach(tag => {
                const tagEl = tagContainer.createEl('div', { cls: 'smart-qa-tag-item' });
                const checkbox = tagEl.createEl('input', { type: 'checkbox' });
                checkbox.value = tag;
                checkbox.id = `tag-${tag.replace('#', '')}`;
                tagEl.createEl('label', { text: tag, attr: { for: checkbox.id } });
                checkbox.addEventListener('change', () => {
                    this.updateTagSelection();
                    this.renderSelectedChips(selectedChips);
                });
            });
        }

        // datalist
        if (tagDataList) {
            tagDataList.empty();
            tags.forEach(tag => {
                const option = document.createElement('option');
                option.value = tag;
                tagDataList.appendChild(option);
            });
        }

        // 保存引用
        if (selectedChips) this.selectedChipsEl = selectedChips;
        if (tagInput) this.tagInputEl = tagInput;

        // 输入事件
        if (tagInput) {
            tagInput.onkeydown = (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const value = tagInput.value.trim();
                    if (!value) return;
                    this.selectTagByValue(value);
                    tagInput.value = '';
                }
            };
            tagInput.oninput = () => {
                this.filterTagList(tagContainer, tagInput.value.trim());
            };
        }

        this.renderSelectedChips(selectedChips);
    }

    async testApiConnectivity() {
        try {
            const hasOpenRouter = !!this.settings.openRouterApiKey;
            const hasGoogle = !!this.settings.googleAiApiKey;
            
            if (!hasOpenRouter && !hasGoogle) {
                new Notice('Please set an API key in Settings first.');
                return;
            }
            
            const chosenModelId = this.currentModelId || this.settings.defaultModel;
            if (!chosenModelId) {
                new Notice('Please choose a model (or configure a default model in Settings).');
                return;
            }
            
            const modelInfo = this.getModelInfo(chosenModelId);
            if (!modelInfo) {
                new Notice('Selected model not found. Please reconfigure in Settings.');
                return;
            }
            
            const fullModelId = `${modelInfo.provider}/${modelInfo.modelId}`;
            
            this.updateStatus('Testing...', `Pinging ${fullModelId}`);
            const messages = [{ role: 'user', content: 'ping' }];
            const resp = await this.aiService.chat(messages, fullModelId, false);
            
            if (resp.error) {
                this.addMessage({ 
                    type: 'system', 
                    content: `❌ Test failed: ${resp.error}`, 
                    timestamp: new Date() 
                });
                new Notice('API Test failed. See panel message for details.');
            } else {
                const preview = resp.content?.slice(0, 100) || '(empty)';
                this.addMessage({ 
                    type: 'system', 
                    content: `✅ API Test OK (model: ${fullModelId})\nPreview: ${preview}`, 
                    timestamp: new Date() 
                });
                new Notice('API Test succeeded!');
            }
        } catch (e) {
            this.addMessage({ 
                type: 'system', 
                content: `❌ Test error: ${e instanceof Error ? e.message : String(e)}`, 
                timestamp: new Date() 
            });
            new Notice('API Test error. Check console/log.');
        } finally {
            this.updateStatus('Ready', this.getStatusText());
        }
    }

    async runUIHealthCheck() {
        // 简化的 UI 健康检查
        const report = { buildMarker: this.buildMarker };
        
        try {
            const container = this.inputContainer;
            const wrapper = container?.querySelector('.smart-qa-input-wrapper');
            const textarea = container?.querySelector('.smart-qa-input');
            const sendBtn = container?.querySelector('.smart-qa-send-btn');
            
            report.elements = {
                container: !!container,
                wrapper: !!wrapper,
                textarea: !!textarea,
                sendBtn: !!sendBtn
            };
            
            if (sendBtn) {
                const cs = getComputedStyle(sendBtn);
                report.sendButton = {
                    display: cs.display,
                    visibility: cs.visibility,
                    opacity: cs.opacity
                };
            }
            
            console.log('[UI Health Check]', report);
            this.addMessage({ 
                type: 'system', 
                content: `UI Debug Report\n${JSON.stringify(report, null, 2)}`, 
                timestamp: new Date() 
            });
        } catch (e) {
            console.error('UI Health Check error:', e);
        }
    }

    newConversation() {
        this.startNewConversation();
        this.messagesContainer.empty();
        this.addMessage({
            type: 'system',
            content: '开始新对话。点击文件选择按钮来选择要分析的文件，然后开始提问。',
            timestamp: new Date()
        });
    }

    openSettings() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.scrollIntoView({ behavior: 'smooth' });
        }
    }

    updateAIService() {
        this.settings = getSettings();
        this.applyFontSize();
        this.initializeAIService();
        const modelSelector = this.inputContainer?.querySelector('.smart-qa-model-selector');
        if (modelSelector) {
            this.populateModelSelector(modelSelector);
        }
    }

    getModelInfo(modelId) {
        return this.settings.customModels.find(model => model.id === modelId);
    }

    getStatusText() {
        let statusText = '';
        
        if (this.currentModelId) {
            const modelInfo = this.getModelInfo(this.currentModelId);
            statusText += `Model: ${modelInfo?.name || 'Unknown'}`;
        } else {
            statusText += 'Model: None selected';
        }
        
        if (this.processedContent) {
            statusText += ` | ${this.processedContent.summary}`;
        } else {
            statusText += ' | Files: All vault';
        }
        
        return statusText;
    }

    updateStatus(status, details) {
        if (this.statusContainer) {
            this.statusContainer.textContent = `${status} | ${details}`;
        }
    }

    updateTokenCount(text, element) {
        const estimatedTokens = this.fileProcessor.estimateTokens(text);
        element.textContent = estimatedTokens.toString();
    }

    updateCollapsedChipLabel() {
        if (!this.collapsedChipEl) return;
        const mode = this.currentSelection.mode;
        let label = 'vault';
        if (mode === 'folder' && typeof this.currentSelection.selection === 'string' && this.currentSelection.selection) {
            label = this.currentSelection.selection.split('/').pop() || 'folder';
        } else if (mode === 'tags' && Array.isArray(this.currentSelection.selection)) {
            label = `tags(${this.currentSelection.selection.length})`;
        }
        this.collapsedChipEl.textContent = label + ' ▼';
    }

    async updateFileSelection(mode) {
        switch (mode) {
            case 'All Files':
                this.currentSelection = { mode: 'all' };
                this.processedContent = null;
                this.updateStatus('Loading...', 'Scanning all files...');
                await this.loadSelectedFiles();
                this.updateStatus('Ready', this.processedContent?.summary || 'Ready');
                break;
                
            case 'Folder':
                this.currentSelection = { mode: 'folder' };
                const statusElement1 = this.fileSelectionContainer.querySelector('.smart-qa-selection-status');
                if (statusElement1) {
                    if (!this.fileProcessor.hasSelectedFolder()) {
                        statusElement1.textContent = '请先在左侧选择知识库文件夹，然后在此处选择子文件夹';
                        // 高亮左侧文件夹选择区域
                        this.highlightFolderSelection();
                    } else {
                        statusElement1.textContent = 'Select a folder to include';
                    }
                }
                break;
                
            case 'Tags':
                this.currentSelection = { mode: 'tags' };
                const statusElement2 = this.fileSelectionContainer.querySelector('.smart-qa-selection-status');
                if (statusElement2) {
                    statusElement2.textContent = 'Select tags to filter files';
                }
                break;
        }
    }
    
    highlightFolderSelection() {
        // 高亮左侧文件夹选择区域
        const folderSection = document.querySelector('.sidebar .config-section:nth-child(4)'); // 文件夹设置是第4个section
        if (folderSection) {
            folderSection.classList.add('folder-hint');
            // 6秒后移除动画类
            setTimeout(() => {
                folderSection.classList.remove('folder-hint');
            }, 6000);
        }
    }

    async loadSelectedFiles() {
        try {
            console.log(`[UIController] Loading files with selection:`, this.currentSelection);

            // 添加调试信息到界面
            this.showDebugInfo(`正在加载文件... 选择模式: ${this.currentSelection.mode}, 选择: ${JSON.stringify(this.currentSelection.selection)}`);

            const files = await this.fileProcessor.getFilesBySelection(
                this.currentSelection.mode,
                this.currentSelection.selection
            );

            console.log(`[UIController] Found ${files.length} files`);

            // 显示找到的文件信息
            this.showDebugInfo(`找到 ${files.length} 个文件`);
            if (files.length > 0) {
                const fileNames = files.slice(0, 5).map(f => f.name || f.path).join(', ');
                this.showDebugInfo(`文件示例: ${fileNames}${files.length > 5 ? '...' : ''}`);
            }

            if (files.length === 0) {
                console.log(`[UIController] No files found for selection`);
                this.showDebugInfo(`❌ 未找到任何文件。请检查文件夹选择是否正确。`);

                // 显示更多调试信息
                if (this.currentSelection.mode === 'folder') {
                    const allFolders = this.fileProcessor.getAllFolders();
                    this.showDebugInfo(`可用文件夹: ${allFolders.join(', ')}`);

                    if (!this.fileProcessor.hasSelectedFolder()) {
                        this.showDebugInfo(`⚠️ 请先在左侧选择知识库文件夹`);
                    }
                }

                this.processedContent = {
                    files: [],
                    totalWordCount: 0,
                    summary: 'No files selected'
                };
                return;
            }

            // 获取完整的文件统计信息（用于显示）
            console.log(`[UIController] Getting file stats for ${files.length} files...`);
            const originalStats = await this.fileProcessor.getFileStats(files);
            console.log(`[UIController] Original stats:`, originalStats);

            this.showDebugInfo(`正在处理文件内容...`);
            const processed = await this.fileProcessor.processFiles(files);
            console.log(`[UIController] Processed content:`, processed);

            // 显示处理结果
            this.showDebugInfo(`✅ 成功处理 ${processed.files.length} 个文件，总词数: ${processed.totalWordCount}`);

            const maxTokens = Math.floor(this.settings.maxContextTokens * 0.8);
            this.processedContent = this.fileProcessor.truncateContent(processed, maxTokens, originalStats);
            this.updateFileSelectionStatus();

        } catch (error) {
            console.error('Failed to load selected files:', error);
            this.showDebugInfo(`❌ 加载文件时出错: ${error.message}`);
            this.processedContent = {
                files: [],
                totalWordCount: 0,
                summary: 'Error loading files'
            };
        }
    }

    showDebugInfo(message) {
        // 在消息区域显示调试信息
        if (this.messagesContainer) {
            const debugEl = this.messagesContainer.createEl('div', {
                cls: 'smart-qa-message smart-qa-debug-message'
            });
            debugEl.createEl('div', {
                cls: 'smart-qa-message-content',
                text: `🔍 ${message}`
            });
            debugEl.createEl('div', {
                cls: 'smart-qa-message-time',
                text: new Date().toLocaleTimeString()
            });

            // 自动滚动到底部
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }

        // 同时输出到控制台
        console.log(`[Debug] ${message}`);
    }

    updateFileSelectionStatus() {
        if (!this.processedContent) return;
        const statusElement = this.fileSelectionContainer.querySelector('.smart-qa-selection-status');
        if (statusElement) {
            statusElement.textContent = this.processedContent.summary;
        }
        this.updateCollapsedChipLabel();
    }

    toggleSelectionCollapse() {
        this.setSelectionCollapse(!this.isSelectionCollapsed);
    }

    setSelectionCollapse(collapsed) {
        this.isSelectionCollapsed = collapsed;
        if (this.selectionContentEl) {
            this.selectionContentEl.style.display = collapsed ? 'none' : 'block';
        }
        if (this.selectionCollapseBtn) {
            setIcon(this.selectionCollapseBtn, collapsed ? 'chevron-down' : 'chevron-up');
            this.selectionCollapseBtn.setAttribute('aria-label', collapsed ? 'Expand file selection' : 'Collapse file selection');
        }
    }

    async showFileSelectionModal() {
        this.setSelectionCollapse(false);
        // 创建模态框
        if (!this.modalOverlay) {
            this.modalOverlay = document.body.createEl('div', { cls: 'smart-qa-modal-overlay' });
            this.modalContent = this.modalOverlay.createEl('div', { cls: 'smart-qa-modal-content' });

            this.modalOverlay.addEventListener('click', (e) => {
                if (e.target === this.modalOverlay) this.hideFileSelectionModal();
            });
        }

        // 清空内容
        this.modalContent.empty();

        // 创建文件选择内容
        const modalHeader = this.modalContent.createEl('div', { cls: 'smart-qa-modal-header' });
        modalHeader.createEl('h3', { text: '文件选择设置', cls: 'smart-qa-modal-title' });
        const closeBtn = modalHeader.createEl('button', {
            cls: 'smart-qa-modal-close',
            attr: { 'aria-label': '关闭' }
        });
        closeBtn.innerHTML = '×';
        closeBtn.addEventListener('click', () => this.hideFileSelectionModal());

        const modalBody = this.modalContent.createEl('div', { cls: 'smart-qa-modal-body' });
        
        // 复制文件选择界面
        await this.createFileSelectionForModal(modalBody);
        
        this.modalOverlay.style.display = 'flex';
        document.body.addClass('smart-qa-modal-open');
    }

    async createFileSelectionForModal(container) {
        // 选择模式按钮组
        const modeButtons = container.createEl('div', { cls: 'smart-qa-mode-buttons' });
        
        const allFilesBtn = modeButtons.createEl('button', { 
            cls: 'smart-qa-mode-btn' + (this.currentSelection.mode === 'all' ? ' smart-qa-mode-btn-active' : ''),
            text: '所有文件'
        });
        
        const folderBtn = modeButtons.createEl('button', { 
            cls: 'smart-qa-mode-btn' + (this.currentSelection.mode === 'folder' ? ' smart-qa-mode-btn-active' : ''),
            text: '文件夹'
        });
        
        const tagBtn = modeButtons.createEl('button', { 
            cls: 'smart-qa-mode-btn' + (this.currentSelection.mode === 'tags' ? ' smart-qa-mode-btn-active' : ''),
            text: '标签'
        });

        // 文件选择区域
        const fileOptions = container.createEl('div', { cls: 'smart-qa-file-options' });
        fileOptions.style.display = this.currentSelection.mode === 'all' ? 'none' : 'block';
        
        // 文件夹选择器
        const folderSelector = fileOptions.createEl('div', { cls: 'smart-qa-folder-selector' });
        folderSelector.style.display = this.currentSelection.mode === 'folder' ? 'block' : 'none';
        folderSelector.createEl('label', { text: '选择文件夹:', cls: 'smart-qa-label' });
        const folderSelect = folderSelector.createEl('select', { cls: 'smart-qa-select' });
        
        // 标签选择器
        const tagSelector = fileOptions.createEl('div', { cls: 'smart-qa-tag-selector' });
        tagSelector.style.display = this.currentSelection.mode === 'tags' ? 'block' : 'none';
        tagSelector.createEl('label', { text: '选择标签:', cls: 'smart-qa-label' });

        // 标签输入
        const tagInputWrapper = tagSelector.createEl('div', { cls: 'smart-qa-tag-input-wrapper' });
        const tagInput = tagInputWrapper.createEl('input', { 
            type: 'text', 
            cls: 'smart-qa-tag-input', 
            placeholder: '输入以筛选或按 Enter 添加，例如 #project' 
        });
        const tagDataList = tagInputWrapper.createEl('datalist', { attr: { id: 'modal-tag-datalist' } });

        // 已选标签 chips
        const selectedChips = tagSelector.createEl('div', { cls: 'smart-qa-selected-chips' });

        // 标签复选列表
        const tagContainer = tagSelector.createEl('div', { cls: 'smart-qa-tag-container' });

        // 状态显示
        const selectionStatus = container.createEl('div', { cls: 'smart-qa-selection-status' });
        this.updateModalSelectionStatus(selectionStatus);

        // 确认按钮
        const confirmBtn = container.createEl('button', { 
            cls: 'smart-qa-btn smart-qa-btn-primary', 
            text: '确认选择',
            style: 'margin-top: 16px; width: 100%;'
        });
        confirmBtn.addEventListener('click', () => {
            this.hideFileSelectionModal();
        });

        // 填充选择器内容
        await this.populateModalSelectors(folderSelect, tagContainer, tagDataList, selectedChips, tagInput, selectionStatus);
        
        // 事件监听器
        [allFilesBtn, folderBtn, tagBtn].forEach(btn => {
            btn.addEventListener('click', () => {
                // 移除所有按钮的active类
                modeButtons.querySelectorAll('.smart-qa-mode-btn').forEach(b => {
                    b.classList.remove('smart-qa-mode-btn-active');
                });
                // 给当前按钮添加active类
                btn.classList.add('smart-qa-mode-btn-active');

                // 隐藏所有选择器
                folderSelector.style.display = 'none';
                tagSelector.style.display = 'none';
                fileOptions.style.display = 'none';

                // 显示相应的选择器
                if (btn.textContent === '文件夹') {
                    fileOptions.style.display = 'block';
                    folderSelector.style.display = 'block';
                } else if (btn.textContent === '标签') {
                    fileOptions.style.display = 'block';
                    tagSelector.style.display = 'block';
                }

                this.updateModalFileSelection(btn.textContent || '', selectionStatus);
            });
        });
        
        // 文件夹选择事件
        folderSelect.addEventListener('change', () => {
            const selectedFolder = folderSelect.value;
            this.currentSelection = { mode: 'folder', selection: selectedFolder };
            this.processedContent = null;
            this.loadSelectedFiles().then(() => {
                this.updateModalSelectionStatus(selectionStatus);
                this.updateCollapsedChipLabel();
                this.updateFileSelectionStatus();
            });
        });
    }

    async populateModalSelectors(folderSelect, tagContainer, tagDataList, selectedChips, tagInput, selectionStatus) {
        // 填充文件夹选择器
        const folders = this.fileProcessor.getAllFolders();
        folderSelect.empty();
        folderSelect.createEl('option', { value: '', text: '选择一个文件夹...' });
        folders.forEach(folder => {
            const option = folderSelect.createEl('option', { value: folder, text: folder });
            if (this.currentSelection.mode === 'folder' && this.currentSelection.selection === folder) {
                option.selected = true;
            }
        });

        // 获取标签与计数
        const tagCounts = await this.fileProcessor.getTagCounts();
        const tags = [...tagCounts].sort((a, b) => b.count - a.count || a.tag.localeCompare(b.tag)).map(x => x.tag);
        
        // 渲染标签复选
        tagContainer.empty();
        if (tags.length === 0) {
            tagContainer.createEl('div', { text: '在您的知识库中没有找到标签', cls: 'smart-qa-no-tags' });
        } else {
            tags.forEach(tag => {
                const tagEl = tagContainer.createEl('div', { cls: 'smart-qa-tag-item' });
                const checkbox = tagEl.createEl('input', { type: 'checkbox' });
                checkbox.value = tag;
                checkbox.id = `modal-tag-${tag.replace('#', '')}`;
                
                // 如果当前选择包含此标签，则勾选
                if (this.currentSelection.mode === 'tags' && 
                    Array.isArray(this.currentSelection.selection) && 
                    this.currentSelection.selection.includes(tag)) {
                    checkbox.checked = true;
                }
                
                tagEl.createEl('label', { text: tag, attr: { for: checkbox.id } });
                checkbox.addEventListener('change', () => {
                    this.updateModalTagSelection(tagContainer, selectedChips, selectionStatus);
                });
            });
        }

        // datalist
        if (tagDataList) {
            tagDataList.empty();
            tags.forEach(tag => {
                const option = document.createElement('option');
                option.value = tag;
                tagDataList.appendChild(option);
            });
        }

        // 输入事件
        if (tagInput) {
            tagInput.onkeydown = (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const value = tagInput.value.trim();
                    if (!value) return;
                    this.selectModalTagByValue(value, tagContainer, selectedChips, selectionStatus);
                    tagInput.value = '';
                }
            };
            tagInput.oninput = () => {
                this.filterModalTagList(tagContainer, tagInput.value.trim());
            };
        }

        this.renderModalSelectedChips(selectedChips, tagContainer, selectionStatus);
    }

    updateModalFileSelection(mode, statusElement) {
        switch (mode) {
            case '所有文件':
                this.currentSelection = { mode: 'all' };
                this.processedContent = null;
                this.loadSelectedFiles().then(() => {
                    this.updateModalSelectionStatus(statusElement);
                    this.updateCollapsedChipLabel();
                    this.updateFileSelectionStatus();
                });
                break;
                
            case '文件夹':
                this.currentSelection = { mode: 'folder' };
                statusElement.textContent = '请选择一个文件夹';
                break;
                
            case '标签':
                this.currentSelection = { mode: 'tags' };
                statusElement.textContent = '请选择要过滤的标签';
                break;
        }
    }

    updateModalSelectionStatus(statusElement) {
        if (this.processedContent) {
            statusElement.textContent = this.processedContent.summary;
        } else {
            switch (this.currentSelection.mode) {
                case 'all':
                    statusElement.textContent = '知识库中的所有文件都将被包含';
                    break;
                case 'folder':
                    statusElement.textContent = '请选择一个文件夹';
                    break;
                case 'tags':
                    statusElement.textContent = '请选择要过滤的标签';
                    break;
            }
        }
    }

    updateModalTagSelection(tagContainer, selectedChips, statusElement) {
        const checkboxes = tagContainer.querySelectorAll('input[type="checkbox"]:checked');
        const selectedTags = Array.from(checkboxes).map(cb => cb.value);
        
        if (selectedTags.length > 0) {
            this.currentSelection = { mode: 'tags', selection: selectedTags };
            this.processedContent = null;
            this.loadSelectedFiles().then(() => {
                this.updateModalSelectionStatus(statusElement);
                this.updateCollapsedChipLabel();
                this.updateFileSelectionStatus();
            });
        } else {
            statusElement.textContent = '请至少选择一个标签';
        }
        
        this.renderModalSelectedChips(selectedChips, tagContainer, statusElement);
    }

    renderModalSelectedChips(wrapper, tagContainer, statusElement) {
        if (!wrapper) return;
        wrapper.empty();
        
        const selected = Array.from(tagContainer.querySelectorAll('input[type="checkbox"]:checked')).map(cb => cb.value);
            
        selected.forEach(tag => {
            const chip = wrapper.createEl('span', { cls: 'smart-qa-chip' });
            chip.createSpan({ text: tag });
            const btn = chip.createEl('button', { text: '×', attr: { 'aria-label': `移除 ${tag}` } });
            btn.addEventListener('click', () => {
                const cb = tagContainer.querySelector(`input[type="checkbox"][value="${tag}"]`);
                if (cb) {
                    cb.checked = false;
                    this.updateModalTagSelection(tagContainer, wrapper, statusElement);
                }
            });
        });
    }

    selectModalTagByValue(value, tagContainer, selectedChips, statusElement) {
        let tag = value;
        if (!tag.startsWith('#')) {
            tag = '#' + tag;
        }
        
        if (!this.availableTags.includes(tag)) {
            new Notice('在知识库中没有找到该标签');
            return;
        }
        
        const checkbox = tagContainer.querySelector(`input[type="checkbox"][value="${tag}"]`);
        if (checkbox && !checkbox.checked) {
            checkbox.checked = true;
            this.updateModalTagSelection(tagContainer, selectedChips, statusElement);
        }
    }

    filterModalTagList(container, query) {
        const items = container.querySelectorAll('.smart-qa-tag-item');
        items.forEach((el) => {
            const label = el.innerText.toLowerCase();
            el.style.display = query ? (label.includes(query.toLowerCase()) ? '' : 'none') : '';
        });
    }

    hideFileSelectionModal() {
        if (this.modalOverlay) {
            this.modalOverlay.style.display = 'none';
            document.body.removeClass('smart-qa-modal-open');
        }
    }

    selectTagByValue(value) {
        let tag = value;
        if (!tag.startsWith('#')) {
            tag = '#' + tag;
        }
        
        if (!this.availableTags.includes(tag)) {
            new Notice('Tag not found in vault');
            return;
        }
        
        const checkbox = this.fileSelectionContainer.querySelector(
            `.smart-qa-tag-item input[type="checkbox"][value="${tag}"]`
        );
        if (checkbox && !checkbox.checked) {
            checkbox.checked = true;
            this.updateTagSelection();
            this.renderSelectedChips(this.selectedChipsEl);
        }
    }

    filterTagList(container, query) {
        const items = container.querySelectorAll('.smart-qa-tag-item');
        items.forEach((el) => {
            const label = el.innerText.toLowerCase();
            el.style.display = query ? (label.includes(query.toLowerCase()) ? '' : 'none') : '';
        });
    }

    renderSelectedChips(wrapper) {
        if (!wrapper) return;
        wrapper.empty();
        
        const selected = Array.from(
            this.fileSelectionContainer.querySelectorAll('.smart-qa-tag-item input[type="checkbox"]')
        )
            .filter(cb => cb.checked)
            .map(cb => cb.value);
            
        selected.forEach(tag => {
            const chip = wrapper.createEl('span', { cls: 'smart-qa-chip' });
            chip.createSpan({ text: tag });
            const btn = chip.createEl('button', { text: '×', attr: { 'aria-label': `Remove ${tag}` } });
            btn.addEventListener('click', () => {
                const cb = this.fileSelectionContainer.querySelector(
                    `.smart-qa-tag-item input[type="checkbox"][value="${tag}"]`
                );
                if (cb) {
                    cb.checked = false;
                    this.updateTagSelection();
                    this.renderSelectedChips(wrapper);
                }
            });
        });
    }

    updateTagSelection() {
        const checkboxes = this.fileSelectionContainer.querySelectorAll(
            '.smart-qa-tag-item input[type="checkbox"]:checked'
        );
        const selectedTags = Array.from(checkboxes).map(cb => cb.value);
        
        if (selectedTags.length > 0) {
            this.currentSelection = { mode: 'tags', selection: selectedTags };
            this.processedContent = null;
            this.loadSelectedFiles().then(() => {
                this.updateFileSelectionStatus();
            });
        } else {
            const statusElement = this.fileSelectionContainer.querySelector('.smart-qa-selection-status');
            if (statusElement) {
                statusElement.textContent = 'Please select at least one tag';
            }
        }
    }

    async addMessage(message) {
        const messageEl = this.messagesContainer.createEl('div', { 
            cls: `smart-qa-message smart-qa-message-${message.type}` 
        });
        const contentEl = messageEl.createEl('div', { cls: 'smart-qa-message-content' });

        if (message.type === 'assistant' || message.type === 'system') {
            contentEl.empty();
            await this.renderMarkdownTo(contentEl, message.content);
        } else {
            contentEl.setText(message.content);
        }

        if (message.type === 'assistant') {
            this.addMessageActions(contentEl, message.content);
        }

        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    addMessageActions(contentEl, content) {
        contentEl.addClass('smart-qa-message-content-with-actions');
        const actionsEl = contentEl.createEl('div', { cls: 'smart-qa-message-actions' });
        
        // 复制按钮
        const copyBtn = actionsEl.createEl('button', { 
            cls: 'smart-qa-btn smart-qa-btn-icon-action',
            title: '复制回答'
        });
        copyBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>`;
        
        copyBtn.addEventListener('click', async () => {
            await navigator.clipboard.writeText(content);
            copyBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20,6 9,17 4,12"/></svg>`;
            copyBtn.title = '已复制！';
            setTimeout(() => {
                copyBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>`;
                copyBtn.title = '复制回答';
            }, 2000);
        });

        // 新建笔记按钮
        const createNoteBtn = actionsEl.createEl('button', { 
            cls: 'smart-qa-btn smart-qa-btn-icon-action',
            title: '新建笔记'
        });
        createNoteBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14,2 14,8 20,8"/><line x1="12" y1="18" x2="12" y2="12"/><line x1="9" y1="15" x2="15" y2="15"/></svg>`;
        
        createNoteBtn.addEventListener('click', () => {
            this.createNoteFromResponse(content);
        });
    }

    async createNoteFromResponse(content) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
            const filename = `AI回答-${timestamp}.md`;
            
            const noteContent = `# AI 回答

创建时间：${new Date().toLocaleString('zh-CN')}

---

${content}

---

*此笔记由 Smart QA 插件自动生成*`;

            // 在浏览器环境中，我们创建一个下载链接
            const blob = new Blob([noteContent], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            new Notice('笔记已下载到本地');
        } catch (error) {
            console.error('创建笔记失败:', error);
            new Notice(`创建笔记失败: ${error.message}`);
        }
    }

    async saveConversationAsNote() {
        if (this.conversationHistory.length === 0) {
            new Notice('No conversation to save');
            return;
        }

        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
            const filename = `Smart QA 对话-${timestamp}.md`;
            
            let noteContent = `# Smart QA 对话记录

创建时间：${new Date().toLocaleString('zh-CN')}

---

`;

            this.conversationHistory.forEach((msg, index) => {
                const role = msg.role === 'user' ? '用户' : msg.role === 'assistant' ? 'AI助手' : '系统';
                noteContent += `## ${role}\n\n${msg.content}\n\n---\n\n`;
            });

            noteContent += '*此对话记录由 Smart QA 插件自动生成*';

            const blob = new Blob([noteContent], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            new Notice('对话记录已下载到本地');
        } catch (error) {
            console.error('保存对话失败:', error);
            new Notice(`保存对话失败: ${error.message}`);
        }
    }

    async handleUserMessage(message) {
        if (this.isProcessing) {
            return;
        }
        
        // 确保使用最新的设置
        this.settings = getSettings();
        this.applyFontSize();

        if (!this.settings.openRouterApiKey && !this.settings.googleAiApiKey) {
            this.addMessage({
                type: 'system',
                content: 'Please configure your API keys in settings before using Smart QA.',
                timestamp: new Date()
            });
            return;
        }

        this.isProcessing = true;

        const userMessage = { role: 'user', content: message };
        this.conversationHistory.push(userMessage);

        this.addMessage({
            type: 'user',
            content: message,
            timestamp: new Date()
        });

        this.updateStatus('Processing...', 'Getting AI response...');

        try {
            if (!this.processedContent) {
                this.updateStatus('Processing...', 'Loading files...');
                await this.loadSelectedFiles();
            }

            let systemPrompt = 'You are Smart QA, an AI assistant helping users analyze their Obsidian knowledge base. Provide clear, helpful answers based on the context provided. If you reference specific information, mention which files it comes from.\n\n';
            
            if (this.processedContent && this.processedContent.files.length > 0) {
                const contextContent = this.fileProcessor.formatForAI(this.processedContent);
                systemPrompt += contextContent;
            } else {
                systemPrompt += 'No files are currently selected or accessible.';
            }

            const messages = [
                {
                    role: 'system',
                    content: systemPrompt
                },
                ...this.conversationHistory
            ];

            const model = this.currentModelId;
            if (!model) {
                this.addMessage({
                    type: 'system',
                    content: 'Please select a model first.',
                    timestamp: new Date()
                });
                return;
            }

            console.log('Current model ID:', model);
            console.log('Available models:', this.settings.customModels);

            const modelInfo = this.getModelInfo(model);
            if (!modelInfo) {
                this.addMessage({
                    type: 'system',
                    content: `Selected model not found. Model ID: ${model}. Available models: ${this.settings.customModels.map(m => m.id).join(', ')}`,
                    timestamp: new Date()
                });
                return;
            }

            const fullModelId = `${modelInfo.provider}/${modelInfo.modelId}`;

            const loadingMessageEl = this.messagesContainer.createEl('div', {
                cls: 'smart-qa-message smart-qa-message-assistant smart-qa-message-loading'
            });
            const loadingContentEl = loadingMessageEl.createEl('div', {
                cls: 'smart-qa-message-content smart-qa-message-content-loading'
            });
            const dotsWrapper = loadingContentEl.createEl('span', {
                cls: 'smart-qa-loading-dots',
                attr: { 'aria-label': 'Loading response' }
            });
            for (let i = 0; i < 3; i++) {
                dotsWrapper.createEl('span');
            }
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;

            let assistantMessageEl = loadingMessageEl;
            let assistantContentEl = loadingContentEl;
            let loadingActive = true;

            const ensureAssistantMessage = () => {
                if (!assistantMessageEl || !assistantContentEl) {
                    assistantMessageEl = this.messagesContainer.createEl('div', { cls: 'smart-qa-message smart-qa-message-assistant' });
                    assistantContentEl = assistantMessageEl.createEl('div', { cls: 'smart-qa-message-content' });
                }
                return assistantContentEl;
            };

            const stopLoadingPlaceholder = () => {
                if (loadingActive && assistantMessageEl && assistantContentEl) {
                    assistantMessageEl.classList.remove('smart-qa-message-loading');
                    assistantContentEl.classList.remove('smart-qa-message-content-loading');
                    assistantContentEl.empty();
                    loadingActive = false;
                }
            };

            const removeLoadingPlaceholder = () => {
                loadingActive = false;
                if (assistantMessageEl && assistantMessageEl.isConnected) {
                    assistantMessageEl.remove();
                }
                assistantMessageEl = null;
                assistantContentEl = null;
            };

            let fullResponse = '';

            for await (const chunk of this.aiService.streamChat(messages, fullModelId)) {
                if (chunk.error) {
                    removeLoadingPlaceholder();
                    this.addMessage({
                        type: 'system',
                        content: `Error: ${chunk.error}`,
                        timestamp: new Date()
                    });
                    break;
                }

                if (chunk.content) {
                    stopLoadingPlaceholder();
                    const targetEl = ensureAssistantMessage();
                    fullResponse += chunk.content;
                    targetEl.empty();
                    await this.renderMarkdownTo(targetEl, fullResponse);
                    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                }

                if (chunk.done) {
                    break;
                }
            }

            if (fullResponse && assistantContentEl) {
                this.addMessageActions(assistantContentEl, fullResponse);
            }

            if (!fullResponse) {
                const fallback = await this.aiService.chat(messages, fullModelId, false);
                if (fallback.error) {
                    removeLoadingPlaceholder();
                    this.addMessage({
                        type: 'system',
                        content: `Error: ${fallback.error}`,
                        timestamp: new Date()
                    });
                } else if (fallback.content) {
                    fullResponse = fallback.content;
                    const targetEl = ensureAssistantMessage();
                    stopLoadingPlaceholder();
                    targetEl.empty();
                    await this.renderMarkdownTo(targetEl, fullResponse);
                    this.addMessageActions(targetEl, fullResponse);
                }
            }

            if (fullResponse) {
                const assistantAIMessage = {
                    role: 'assistant',
                    content: fullResponse
                };
                this.conversationHistory.push(assistantAIMessage);
            } else {
                removeLoadingPlaceholder();
                this.addMessage({
                    type: 'system',
                    content: 'No response received from the model. Please verify API key and model settings.',
                    timestamp: new Date()
                });
            }

            const tokenCount = this.aiService.estimateTokens(
                messages.map(m => m.content).join(''), 
                fullModelId
            );
            this.updateStatus('Ready', `${this.getStatusText()} | Tokens: ~${tokenCount}`);

        } catch (error) {
            removeLoadingPlaceholder();
            this.addMessage({
                type: 'system',
                content: `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
                timestamp: new Date()
            });
            this.updateStatus('Ready', 'Error occurred');
        } finally {
            if (loadingActive) {
                removeLoadingPlaceholder();
            }
            this.isProcessing = false;
        }
    }
}

console.log('[UI Controller] Module loaded');
