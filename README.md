# Smart QA - Obsidian AI Knowledge Base Plugin

智能知识库问答插件，支持与整个 Obsidian 知识库进行 AI 对话。

## 功能特性

- 🤖 多 AI 模型支持（Claude、GPT、Gemini）
- 📁 灵活文件选择（文件夹、标签、全库）
- 💬 多轮对话记忆
- 📝 对话内容生成笔记
- 🎨 Notion 风格简洁界面
- ⚡ 流式响应体验
- 🔍 信息源追踪

## 开发环境设置

### 安装依赖
```bash
npm install
```

### 开发模式（热重载）
```bash
npm run dev
```
此模式会监听文件变化并自动重新构建。

### 构建生产版本
```bash
npm run build
```

### 在 Obsidian 中测试

1. 构建项目：`npm run build`
2. 将整个插件文件夹复制到你的 Obsidian vault 的 `.obsidian/plugins/` 目录下
3. 在 Obsidian 设置中启用 "Smart QA" 插件
4. 使用快捷键 `Cmd/Ctrl + Shift + Q` 打开插件面板

## 项目结构

```
smart-qa/
├── src/
│   ├── view.ts          # 侧边栏视图组件
│   └── settings.ts      # 设置配置
├── main.ts              # 插件入口文件
├── manifest.json        # 插件清单
├── package.json         # 项目配置
├── tsconfig.json        # TypeScript 配置
├── esbuild.config.js    # 构建配置
└── README.md           # 本文件
```

## 设置 API

在 Obsidian 设置页面找到 "Smart QA" 部分：

1. **OpenRouter API Key**: 用于访问多种 AI 模型
2. **Google AI API Key**: 用于访问 Gemini 模型
3. **Default Model**: 选择默认使用的 AI 模型
4. **Enable Hybrid Mode**: 启用预筛选模式以减少 Token 消耗

## 使用方法

1. 点击右侧边栏的消息图标或使用快捷键打开 Smart QA 面板
2. 在文件选择区域选择要包含的文件范围
3. 在输入框中输入问题
4. 查看 AI 回答，可以看到信息来源
5. 使用"保存为笔记"功能将对话内容保存到知识库

## 开发待办

- [x] 基础插件结构
- [x] TypeScript + esbuild 构建环境  
- [x] 开发热重载配置
- [ ] 侧边栏视图实现
- [ ] AI API 集成
- [ ] 文件内容处理
- [ ] Token 计数功能
- [ ] 对话历史管理
- [ ] 笔记生成功能

## 贡献指南

欢迎提交 Issues 和 Pull Requests！

## 许可证

MIT License