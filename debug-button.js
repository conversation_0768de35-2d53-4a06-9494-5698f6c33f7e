// 调试脚本：检查 Smart QA Tab按钮状态和弹窗状态
// 在 Obsidian 开发者控制台中运行此代码

(function debugSmartQATabsAndModal() {
    console.log('=== Smart QA Tab按钮和弹窗调试 ===');

    // 1. 检查Tab按钮状态
    console.log('\n--- Tab按钮状态检查 ---');
    const modeButtons = document.querySelectorAll('.smart-qa-mode-btn');
    console.log('找到Tab按钮数量:', modeButtons.length);

    modeButtons.forEach((btn, index) => {
        const isActive = btn.classList.contains('smart-qa-mode-btn-active');
        console.log(`Tab ${index}: "${btn.textContent}" - 选中状态: ${isActive ? '✅' : '❌'}`);
        console.log(`  CSS类: ${btn.className}`);
        console.log(`  计算样式:`, {
            background: getComputedStyle(btn).backgroundColor,
            color: getComputedStyle(btn).color
        });
    });

    // 2. 检查弹窗状态
    console.log('\n--- 弹窗状态检查 ---');
    const modalOverlay = document.querySelector('.smart-qa-modal-overlay');
    if (modalOverlay) {
        console.log('弹窗存在');
        console.log('关闭按钮: 已移除，点击蒙层即可关闭');

        // 检查弹窗内的Tab按钮
        const modalModeButtons = modalOverlay.querySelectorAll('.smart-qa-mode-btn');
        console.log('弹窗内Tab按钮数量:', modalModeButtons.length);
        modalModeButtons.forEach((btn, index) => {
            const isActive = btn.classList.contains('smart-qa-mode-btn-active');
            console.log(`弹窗Tab ${index}: "${btn.textContent}" - 选中状态: ${isActive ? '✅' : '❌'}`);
        });
    } else {
        console.log('弹窗不存在，请先打开弹窗');
    }

    // 3. 提供测试功能
    console.log('\n--- 测试功能 ---');
    console.log('可用测试命令:');
    console.log('1. 测试Tab切换: testTabSwitch("Folder") 或 testTabSwitch("Tags")');
    console.log('2. 打开弹窗: openModal()');
    console.log('3. 关闭弹窗: closeModal() (模拟点击蒙层关闭)');

    // 定义测试函数
    window.testTabSwitch = function(tabName) {
        console.log(`\n测试切换到: ${tabName}`);
        const targetBtn = Array.from(document.querySelectorAll('.smart-qa-mode-btn'))
            .find(btn => btn.textContent === tabName);

        if (targetBtn) {
            console.log('点击前状态:');
            document.querySelectorAll('.smart-qa-mode-btn').forEach(btn => {
                console.log(`  ${btn.textContent}: ${btn.classList.contains('smart-qa-mode-btn-active') ? '选中' : '未选中'}`);
            });

            targetBtn.click();

            setTimeout(() => {
                console.log('点击后状态:');
                document.querySelectorAll('.smart-qa-mode-btn').forEach(btn => {
                    console.log(`  ${btn.textContent}: ${btn.classList.contains('smart-qa-mode-btn-active') ? '选中' : '未选中'}`);
                });
            }, 100);
        } else {
            console.error(`未找到 ${tabName} 按钮`);
        }
    };

    window.openModal = function() {
        console.log('\n测试打开弹窗');
        const collapsedChip = document.querySelector('.smart-qa-collapsed-chip');
        if (collapsedChip && collapsedChip.style.display !== 'none') {
            collapsedChip.click();
            setTimeout(() => {
                const overlay = document.querySelector('.smart-qa-modal-overlay');
                console.log(`弹窗打开后，蒙层是否存在: ${!!overlay}`);
            }, 100);
        } else {
            console.log('折叠按钮不可见，尝试其他方式...');
        }
    };

    window.closeModal = function() {
        console.log('\n测试关闭弹窗');
        const overlay = document.querySelector('.smart-qa-modal-overlay');
        if (overlay && overlay.style.display !== 'none') {
            overlay.click();
        } else {
            console.log('未找到可见的弹窗蒙层');
        }
    };
})();

console.log('\n=== 使用说明 ===');
console.log('1. 先运行上面的检查，查看当前状态');
console.log('2. 使用 testTabSwitch("Folder") 测试Tab切换');
console.log('3. 使用 openModal() 打开弹窗并检查蒙层');
console.log('4. 使用 closeModal() 关闭弹窗');
